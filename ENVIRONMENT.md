# Environment Variables Documentation

This document provides comprehensive documentation for all environment variables used in the Spark AI Experience application.

## Overview

The application uses environment variables for configuration management across different deployment environments (development, staging, production). All variables are validated at startup to ensure proper configuration.

## Environment Files

- `.env.example` - Template with all available variables and their descriptions
- `.env.development` - Development-specific configuration
- `.env.staging` - Staging/preview environment configuration  
- `.env.production` - Production environment configuration
- `.env` - Local development overrides (not committed to git)

## Environment Detection

The application automatically detects the current environment using:

1. `VERCEL_ENV` (Vercel deployment environment)
2. `NODE_ENV` (Node.js environment)
3. `VITE_ENV` (Custom environment override)

## Required Variables

### API Configuration

#### `VITE_API_ENDPOINT` (Required)
- **Description**: Z.AI API endpoint URL
- **Default**: `https://open.bigmodel.cn/api/paas/v4`
- **Format**: Valid URL
- **Example**: `https://open.bigmodel.cn/api/paas/v4`

#### `VITE_API_KEY` (Required)
- **Description**: Z.AI API authentication key
- **Format**: `{32 alphanumeric chars}.{16 alphanumeric chars}`
- **Example**: `abcd1234efgh5678ijkl9012mnop3456.qrst7890uvwx1234`
- **Security**: Never commit this value to version control

#### `VITE_API_VERSION` (Required)
- **Description**: Z.AI API version
- **Default**: `v4`
- **Allowed Values**: `v3`, `v4`

### Database Configuration

#### `POSTGRES_URL` (Required for Production)
- **Description**: Primary PostgreSQL connection string
- **Format**: `postgresql://user:password@host:port/database`
- **Example**: `postgresql://user:pass@localhost:5432/spark_ai`

#### `POSTGRES_PRISMA_URL` (Required for Production)
- **Description**: Prisma-optimized PostgreSQL connection string
- **Format**: `postgresql://user:password@host:port/database`

#### `POSTGRES_URL_NON_POOLING` (Required for Production)
- **Description**: Non-pooled PostgreSQL connection string
- **Format**: `postgresql://user:password@host:port/database`

## Optional Variables

### Model Configuration

#### `VITE_DEFAULT_MODEL`
- **Description**: Default AI model to use
- **Default**: `glm-4-flash`
- **Options**: `glm-4-flash`, `glm-4`, `glm-4-plus`

#### `VITE_MAX_TOKENS`
- **Description**: Maximum tokens per request
- **Default**: `4000`
- **Range**: `1` - `32000`

#### `VITE_TEMPERATURE`
- **Description**: AI model temperature (creativity level)
- **Default**: `0.7`
- **Range**: `0.0` - `2.0`

#### `VITE_API_TIMEOUT`
- **Description**: API request timeout in milliseconds
- **Default**: `30000`
- **Range**: `1000` - `300000`

### Video Generation Configuration

#### `VITE_VIDEO_MODEL`
- **Description**: Video generation model
- **Default**: `cogvideox-3`

#### `VITE_VIDEO_MAX_WIDTH`
- **Description**: Maximum video width in pixels
- **Default**: `1920`
- **Range**: `256` - `4096`

#### `VITE_VIDEO_MAX_HEIGHT`
- **Description**: Maximum video height in pixels
- **Default**: `1080`
- **Range**: `256` - `4096`

#### `VITE_VIDEO_MAX_DURATION`
- **Description**: Maximum video duration in seconds
- **Default**: `30`
- **Range**: `1` - `300`

#### `VITE_VIDEO_MAX_STEPS`
- **Description**: Maximum video generation steps
- **Default**: `100`
- **Range**: `1` - `1000`

### Retry Configuration

#### `VITE_MAX_RETRIES`
- **Description**: Maximum number of API request retries
- **Default**: `3`
- **Range**: `0` - `10`

#### `VITE_RETRY_DELAY`
- **Description**: Base delay between retries in milliseconds
- **Default**: `2000`
- **Range**: `100` - `30000`

#### `VITE_EXPONENTIAL_BACKOFF`
- **Description**: Enable exponential backoff for retries
- **Default**: `true`
- **Type**: Boolean

### Security Configuration

#### `VITE_RATE_LIMIT_ENABLED`
- **Description**: Enable rate limiting
- **Default**: `true`
- **Type**: Boolean

#### `VITE_RATE_LIMIT_MAX_REQUESTS`
- **Description**: Maximum requests per time window
- **Default**: `60`
- **Range**: `1` - `10000`

#### `VITE_RATE_LIMIT_WINDOW_MS`
- **Description**: Rate limiting time window in milliseconds
- **Default**: `60000` (1 minute)
- **Range**: `1000` - `3600000`

#### `VITE_MAX_INPUT_LENGTH`
- **Description**: Maximum input length for user messages
- **Default**: `10000`
- **Range**: `100` - `100000`

#### `VITE_LOG_SECURITY_EVENTS`
- **Description**: Enable security event logging
- **Default**: `false`
- **Type**: Boolean

#### `VITE_CORS_ORIGINS`
- **Description**: Allowed CORS origins (comma-separated)
- **Default**: `localhost,127.0.0.1`
- **Format**: Comma-separated list of domains
- **Example**: `localhost,127.0.0.1,myapp.vercel.app`

### Monitoring Configuration

#### `VITE_DEBUG_MODE`
- **Description**: Enable debug mode with verbose logging
- **Default**: `false` (production), `true` (development)
- **Type**: Boolean

#### `VITE_ANALYTICS_ENABLED`
- **Description**: Enable analytics tracking
- **Default**: `true` (production), `false` (development)
- **Type**: Boolean

#### `VITE_ERROR_TRACKING_ENABLED`
- **Description**: Enable error tracking and reporting
- **Default**: `true` (production), `false` (development)
- **Type**: Boolean

#### `VITE_PERFORMANCE_MONITORING`
- **Description**: Enable performance monitoring
- **Default**: `true` (production), `false` (development)
- **Type**: Boolean

### Feature Flags

#### `VITE_MOCK_API_ENABLED`
- **Description**: Use mock API responses when API key is missing
- **Default**: `true` (development), `false` (production)
- **Type**: Boolean

#### `VITE_FEATURE_FLAGS_ENABLED`
- **Description**: Enable feature flag system
- **Default**: `false`
- **Type**: Boolean

#### `VITE_A_B_TESTING_ENABLED`
- **Description**: Enable A/B testing framework
- **Default**: `false`
- **Type**: Boolean

## Environment-Specific Configurations

### Development Environment

```bash
# Relaxed security settings
VITE_RATE_LIMIT_ENABLED=false
VITE_DEBUG_MODE=true
VITE_MOCK_API_ENABLED=true

# Extended limits for testing
VITE_MAX_INPUT_LENGTH=50000
VITE_RATE_LIMIT_MAX_REQUESTS=1000

# Local database
POSTGRES_URL=postgresql://localhost:5432/spark_ai_dev
```

### Staging Environment

```bash
# Moderate security settings
VITE_RATE_LIMIT_ENABLED=true
VITE_RATE_LIMIT_MAX_REQUESTS=120
VITE_DEBUG_MODE=false

# Monitoring enabled
VITE_ANALYTICS_ENABLED=true
VITE_ERROR_TRACKING_ENABLED=true

# Vercel Postgres
POSTGRES_URL=postgresql://...vercel-postgres-staging...
```

### Production Environment

```bash
# Strict security settings
VITE_RATE_LIMIT_ENABLED=true
VITE_RATE_LIMIT_MAX_REQUESTS=60
VITE_DEBUG_MODE=false

# Full monitoring
VITE_ANALYTICS_ENABLED=true
VITE_ERROR_TRACKING_ENABLED=true
VITE_PERFORMANCE_MONITORING=true

# Production database
POSTGRES_URL=postgresql://...vercel-postgres-production...
```

## Validation Rules

The application validates all environment variables at startup:

- **Required variables**: Must be present and non-empty
- **Type validation**: Numbers, booleans, URLs are validated
- **Range validation**: Numeric values must be within specified ranges
- **Format validation**: API keys, URLs, and other formatted values are validated
- **Custom validation**: Business logic validation (e.g., aspect ratios, API key format)

## Security Best Practices

### 1. API Key Management
- Never commit API keys to version control
- Use different API keys for different environments
- Rotate API keys regularly
- Monitor API key usage

### 2. Database Security
- Use connection pooling in production
- Enable SSL for database connections
- Use read-only replicas when possible
- Regular database backups

### 3. CORS Configuration
- Restrict CORS origins to known domains
- Use wildcard patterns carefully
- Different origins for different environments

### 4. Rate Limiting
- Enable rate limiting in production
- Adjust limits based on usage patterns
- Monitor rate limit violations

## Troubleshooting

### Common Issues

#### 1. API Key Format Error
```
Error: VITE_API_KEY must follow Z.AI format: 32chars.16chars
```
**Solution**: Ensure API key follows the correct format with exactly 32 characters, a dot, then 16 characters.

#### 2. Database Connection Error
```
Error: POSTGRES_URL must be a valid PostgreSQL connection string
```
**Solution**: Verify the database URL format and credentials.

#### 3. Invalid Environment Variable
```
Error: VITE_MAX_TOKENS must be a valid number
```
**Solution**: Check that numeric environment variables contain valid numbers.

### Debugging Environment Issues

1. **Check Environment Detection**:
   ```javascript
   console.log('Environment:', environmentManager.getEnvironment());
   ```

2. **Validate Configuration**:
   ```javascript
   console.log('Config Valid:', environmentManager.isValid());
   console.log('Errors:', environmentManager.getValidationErrors());
   ```

3. **Review Configuration Summary**:
   ```javascript
   console.log('Config Summary:', environmentManager.getConfigSummary());
   ```

## Vercel Deployment

### Setting Environment Variables in Vercel

1. **Via Vercel Dashboard**:
   - Go to Project Settings → Environment Variables
   - Add variables for each environment (Production, Preview, Development)

2. **Via Vercel CLI**:
   ```bash
   vercel env add VITE_API_KEY production
   vercel env add POSTGRES_URL production
   ```

3. **Environment-Specific Variables**:
   - Production: Used for `vercel.app` domain
   - Preview: Used for PR deployments
   - Development: Used for `vercel dev`

### Required Vercel Environment Variables

For production deployment, ensure these variables are set in Vercel:

```bash
# Required
VITE_API_KEY=your_production_api_key
POSTGRES_URL=your_vercel_postgres_url
POSTGRES_PRISMA_URL=your_vercel_postgres_prisma_url
POSTGRES_URL_NON_POOLING=your_vercel_postgres_non_pooling_url

# Recommended
VITE_CORS_ORIGINS=your-domain.vercel.app
VITE_DEBUG_MODE=false
VITE_ANALYTICS_ENABLED=true
```

## Migration Guide

### From Previous Configuration

If migrating from an older configuration system:

1. **Update Environment Files**: Use the new environment-specific files
2. **Validate Variables**: Run validation to check for missing or invalid variables
3. **Update Code**: Use the new `environmentManager` instead of direct `import.meta.env` access
4. **Test Configuration**: Verify all environments work correctly

### Adding New Variables

1. **Add to Validation Rules**: Update `VALIDATION_RULES` in `environment.ts`
2. **Update Interfaces**: Add to appropriate configuration interfaces
3. **Update Documentation**: Add to this documentation file
4. **Update Example Files**: Add to `.env.example` and environment-specific files

## Support

For environment configuration issues:

1. Check this documentation
2. Validate your configuration using the built-in validation
3. Review the console for validation errors
4. Check Vercel deployment logs for environment-related errors

## Changelog

### Version 1.0.0
- Initial environment variables management system
- Support for development, staging, and production environments
- Comprehensive validation and documentation
- Integration with Vercel deployment