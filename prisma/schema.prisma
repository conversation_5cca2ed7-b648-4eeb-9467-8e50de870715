// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_PRISMA_URL") // uses connection pooling
  directUrl = env("POSTGRES_URL_NON_POOLING") // uses a direct connection
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  conversations Conversation[]
  messages      Message[]

  @@map("users")
}

model Conversation {
  id          String   @id @default(cuid())
  title       String?
  userId      String?
  isArchived  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user     User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(cuid())
  content        String
  role           String   // 'user' | 'assistant' | 'system'
  conversationId String
  userId         String?
  metadata       Json?    // For storing additional data like model, tokens, etc.
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("messages")
}

model ApiUsage {
  id          String   @id @default(cuid())
  userId      String?
  model       String
  tokens      Int
  cost        Float?
  endpoint    String
  status      String   // 'success' | 'error'
  errorCode   String?
  errorMessage String?
  createdAt   DateTime @default(now())

  @@map("api_usage")
}

model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}