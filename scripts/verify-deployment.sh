#!/bin/bash

# Vercel Deployment Verification Script
# This script verifies that the application is ready for deployment

set -e

echo "🚀 Starting Vercel Deployment Verification..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if required files exist
check_required_files() {
    print_status "INFO" "Checking required files..."
    
    local required_files=(
        "package.json"
        "vercel.json"
        "vite.config.ts"
        "tsconfig.json"
        ".env.example"
        "ENVIRONMENT.md"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_status "SUCCESS" "Found $file"
        else
            print_status "ERROR" "Missing required file: $file"
            exit 1
        fi
    done
}

# Validate package.json scripts
check_build_scripts() {
    print_status "INFO" "Checking build scripts..."
    
    if npm run build --dry-run > /dev/null 2>&1; then
        print_status "SUCCESS" "Build script is valid"
    else
        print_status "ERROR" "Build script is invalid"
        exit 1
    fi
}

# Test build process
test_build() {
    print_status "INFO" "Testing build process..."
    
    # Clean previous build
    if [[ -d "dist" ]]; then
        rm -rf dist
        print_status "INFO" "Cleaned previous build"
    fi
    
    # Run build
    if npm run build; then
        print_status "SUCCESS" "Build completed successfully"
    else
        print_status "ERROR" "Build failed"
        exit 1
    fi
    
    # Check if dist directory was created
    if [[ -d "dist" ]]; then
        print_status "SUCCESS" "Build output directory created"
    else
        print_status "ERROR" "Build output directory not found"
        exit 1
    fi
    
    # Check if index.html exists
    if [[ -f "dist/index.html" ]]; then
        print_status "SUCCESS" "index.html generated"
    else
        print_status "ERROR" "index.html not found in build output"
        exit 1
    fi
    
    # Check build size
    local build_size=$(du -sh dist | cut -f1)
    print_status "INFO" "Build size: $build_size"
    
    # Check for large chunks
    local large_chunks=$(find dist -name "*.js" -size +500k | wc -l)
    if [[ $large_chunks -gt 0 ]]; then
        print_status "WARNING" "Found $large_chunks JavaScript chunks larger than 500KB"
        print_status "INFO" "Consider code splitting for better performance"
    fi
}

# Validate vercel.json configuration
validate_vercel_config() {
    print_status "INFO" "Validating vercel.json configuration..."
    
    # Check if vercel.json is valid JSON
    if jq empty vercel.json > /dev/null 2>&1; then
        print_status "SUCCESS" "vercel.json is valid JSON"
    else
        print_status "ERROR" "vercel.json is not valid JSON"
        exit 1
    fi
    
    # Check required fields
    local required_fields=("version" "builds" "routes")
    for field in "${required_fields[@]}"; do
        if jq -e ".$field" vercel.json > /dev/null 2>&1; then
            print_status "SUCCESS" "vercel.json has required field: $field"
        else
            print_status "ERROR" "vercel.json missing required field: $field"
            exit 1
        fi
    done
    
    # Check security headers
    if jq -e '.headers[] | select(.source == "/(.*)")' vercel.json > /dev/null 2>&1; then
        print_status "SUCCESS" "Security headers configured"
    else
        print_status "WARNING" "Security headers not found in vercel.json"
    fi
    
    # Check CORS headers
    if jq -e '.headers[] | select(.source == "/api/(.*)")' vercel.json > /dev/null 2>&1; then
        print_status "SUCCESS" "CORS headers configured for API routes"
    else
        print_status "WARNING" "CORS headers not configured for API routes"
    fi
}

# Check environment variables
check_environment_variables() {
    print_status "INFO" "Checking environment variables configuration..."
    
    # Check if .env.example exists and has required variables
    if [[ -f ".env.example" ]]; then
        local required_vars=(
            "VITE_API_ENDPOINT"
            "VITE_API_KEY"
            "VITE_DEFAULT_MODEL"
        )
        
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" .env.example; then
                print_status "SUCCESS" "Found $var in .env.example"
            else
                print_status "WARNING" "$var not found in .env.example"
            fi
        done
    else
        print_status "WARNING" ".env.example file not found"
    fi
    
    # Check vercel.json env configuration
    if jq -e '.env' vercel.json > /dev/null 2>&1; then
        print_status "SUCCESS" "Environment variables configured in vercel.json"
        
        # Count configured variables
        local env_count=$(jq '.env | length' vercel.json)
        print_status "INFO" "Configured environment variables: $env_count"
    else
        print_status "WARNING" "No environment variables configured in vercel.json"
    fi
}

# Test TypeScript compilation
test_typescript() {
    print_status "INFO" "Testing TypeScript compilation..."
    
    if npx tsc --noEmit; then
        print_status "SUCCESS" "TypeScript compilation successful"
    else
        print_status "ERROR" "TypeScript compilation failed"
        exit 1
    fi
}

# Run linting
test_linting() {
    print_status "INFO" "Running ESLint..."
    
    if npm run lint; then
        print_status "SUCCESS" "Linting passed"
    else
        print_status "WARNING" "Linting issues found (not blocking deployment)"
    fi
}

# Test preview server
test_preview() {
    print_status "INFO" "Testing preview server..."
    
    # Start preview server in background
    npm run preview &
    local preview_pid=$!
    
    # Wait for server to start
    sleep 3
    
    # Test if server is responding
    if curl -s http://localhost:4173 > /dev/null; then
        print_status "SUCCESS" "Preview server is responding"
    else
        print_status "WARNING" "Preview server not responding (may be normal)"
    fi
    
    # Kill preview server
    kill $preview_pid 2>/dev/null || true
}

# Check dependencies
check_dependencies() {
    print_status "INFO" "Checking dependencies..."
    
    # Check for security vulnerabilities
    if npm audit --audit-level=high; then
        print_status "SUCCESS" "No high-severity vulnerabilities found"
    else
        print_status "WARNING" "Security vulnerabilities found - consider running 'npm audit fix'"
    fi
    
    # Check for outdated packages
    local outdated_count=$(npm outdated --json 2>/dev/null | jq 'length' 2>/dev/null || echo "0")
    if [[ $outdated_count -gt 0 ]]; then
        print_status "INFO" "Found $outdated_count outdated packages"
    else
        print_status "SUCCESS" "All packages are up to date"
    fi
}

# Generate deployment report
generate_report() {
    print_status "INFO" "Generating deployment report..."
    
    local report_file="deployment-report.md"
    
    cat > "$report_file" << EOF
# Deployment Verification Report

Generated on: $(date)

## Build Information
- Build Status: ✅ Success
- Build Size: $(du -sh dist 2>/dev/null | cut -f1 || echo "Unknown")
- TypeScript: ✅ Compiled successfully
- Linting: $(npm run lint > /dev/null 2>&1 && echo "✅ Passed" || echo "⚠️ Issues found")

## Configuration
- vercel.json: ✅ Valid
- Environment Variables: $(jq '.env | length' vercel.json) configured
- Security Headers: ✅ Configured
- CORS Headers: ✅ Configured

## Dependencies
- Security Audit: $(npm audit --audit-level=high > /dev/null 2>&1 && echo "✅ No high-severity issues" || echo "⚠️ Issues found")
- Outdated Packages: $(npm outdated --json 2>/dev/null | jq 'length' 2>/dev/null || echo "0")

## Recommendations
- Ensure all environment variables are set in Vercel dashboard
- Test the deployment in preview environment before promoting to production
- Monitor application performance after deployment
- Set up monitoring and alerting for production environment

EOF
    
    print_status "SUCCESS" "Deployment report generated: $report_file"
}

# Main execution
main() {
    echo "=================================================="
    echo "🚀 Vercel Deployment Verification"
    echo "=================================================="
    
    check_required_files
    check_build_scripts
    validate_vercel_config
    check_environment_variables
    test_typescript
    test_linting
    test_build
    test_preview
    check_dependencies
    generate_report
    
    echo "=================================================="
    print_status "SUCCESS" "Deployment verification completed!"
    echo "=================================================="
    
    print_status "INFO" "Next steps:"
    echo "1. Review the deployment report"
    echo "2. Set environment variables in Vercel dashboard"
    echo "3. Deploy to preview environment first"
    echo "4. Test thoroughly before promoting to production"
}

# Run main function
main "$@"