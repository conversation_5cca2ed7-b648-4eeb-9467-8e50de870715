#!/bin/bash

# 🚀 Spark AI Experience - Vercel Deployment Script
# Automatisiertes Deployment-Script für Vercel

set -e  # Exit on any error

echo "🚀 Starting Spark AI Experience Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    print_success "All requirements met!"
}

# Clean up sensitive files
cleanup_sensitive_files() {
    print_status "Cleaning up sensitive files..."
    
    # Remove .env file if it exists (should be in .gitignore)
    if [ -f ".env" ]; then
        print_warning "Found .env file. This should not be committed to Git."
        print_status "Please ensure all sensitive data is configured in Vercel Environment Variables."
    fi
    
    # Ensure .gitignore exists and contains necessary entries
    if [ ! -f ".gitignore" ]; then
        print_error ".gitignore file not found!"
        exit 1
    fi
    
    print_success "Cleanup completed!"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed!"
}

# Run linting
run_linting() {
    print_status "Running ESLint..."
    npm run lint
    print_success "Linting completed!"
}

# Build the project
build_project() {
    print_status "Building project..."
    npm run build
    print_success "Build completed!"
}

# Test the build
test_build() {
    print_status "Testing build locally..."
    
    # Check if dist directory exists
    if [ ! -d "dist" ]; then
        print_error "Build directory 'dist' not found!"
        exit 1
    fi
    
    # Check if index.html exists
    if [ ! -f "dist/index.html" ]; then
        print_error "index.html not found in build directory!"
        exit 1
    fi
    
    print_success "Build test passed!"
}

# Git operations
prepare_git() {
    print_status "Preparing Git repository..."
    
    # Check if git repo is initialized
    if [ ! -d ".git" ]; then
        print_status "Initializing Git repository..."
        git init
        git branch -M main
    fi
    
    # Add all files
    git add .
    
    # Check if there are changes to commit
    if git diff --staged --quiet; then
        print_warning "No changes to commit."
    else
        print_status "Committing changes..."
        git commit -m "feat: prepare for vercel deployment - $(date '+%Y-%m-%d %H:%M:%S')"
        print_success "Changes committed!"
    fi
}

# Push to GitHub (if remote exists)
push_to_github() {
    print_status "Checking GitHub remote..."
    
    if git remote get-url origin &> /dev/null; then
        print_status "Pushing to GitHub..."
        git push origin main
        print_success "Pushed to GitHub!"
    else
        print_warning "No GitHub remote found. Please add your GitHub repository:"
        print_warning "git remote add origin https://github.com/yourusername/spark-ai-experience.git"
        print_warning "git push -u origin main"
    fi
}

# Vercel deployment check
check_vercel_config() {
    print_status "Checking Vercel configuration..."
    
    if [ ! -f "vercel.json" ]; then
        print_error "vercel.json not found!"
        exit 1
    fi
    
    print_success "Vercel configuration found!"
}

# Environment variables reminder
env_variables_reminder() {
    print_warning "🔑 IMPORTANT: Ensure the following environment variables are set in Vercel:"
    echo ""
    echo "Required API Configuration:"
    echo "  VITE_API_ENDPOINT = https://open.bigmodel.cn/api/paas/v4"
    echo "  VITE_API_KEY = [YOUR_ZAI_API_KEY]"
    echo "  VITE_API_VERSION = v4"
    echo "  VITE_DEFAULT_MODEL = glm-4-flash"
    echo "  VITE_MAX_TOKENS = 4000"
    echo "  VITE_TEMPERATURE = 0.7"
    echo "  VITE_DEBUG_MODE = false"
    echo ""
    echo "Video Configuration:"
    echo "  VITE_VIDEO_MODEL = cogvideox-3"
    echo "  VITE_VIDEO_MAX_WIDTH = 1920"
    echo "  VITE_VIDEO_MAX_HEIGHT = 1080"
    echo "  VITE_VIDEO_MAX_DURATION = 30"
    echo "  VITE_VIDEO_MAX_STEPS = 100"
    echo ""
    echo "Retry Configuration:"
    echo "  VITE_MAX_RETRIES = 3"
    echo "  VITE_RETRY_DELAY = 2000"
    echo ""
}

# Main deployment function
main() {
    echo "🎯 Spark AI Experience - Vercel Deployment"
    echo "=========================================="
    echo ""
    
    check_requirements
    cleanup_sensitive_files
    install_dependencies
    run_linting
    build_project
    test_build
    check_vercel_config
    prepare_git
    push_to_github
    
    echo ""
    print_success "🎉 Deployment preparation completed!"
    echo ""
    env_variables_reminder
    echo ""
    print_status "Next steps:"
    echo "1. Go to https://vercel.com/dashboard"
    echo "2. Import your GitHub repository"
    echo "3. Configure environment variables (see above)"
    echo "4. Deploy!"
    echo ""
    print_success "🚀 Ready for Vercel deployment!"
}

# Run main function
main "$@"