#!/usr/bin/env tsx

/**
 * Vercel Postgres Database Setup Script
 * Initializes the database schema and seeds initial data
 */

// Set up environment for Node.js
process.env.NODE_ENV = process.env.NODE_ENV || "development";

import { vercelDb } from "../src/lib/database-integration";

async function setupVercelDatabase() {
  console.log("🚀 Setting up Vercel Postgres Database...");
  console.log(`📍 Environment: ${process.env.NODE_ENV}`);

  try {
    // Test database connection
    console.log("📡 Testing database connection...");
    const health = await vercelDb.healthCheck();

    if (!health.connected) {
      throw new Error(`Database connection failed: ${health.error}`);
    }

    console.log(
      `✅ Database connected successfully (${health.latency}ms latency)`
    );
    console.log(`🌍 Region: ${health.region}`);

    // Initialize database schema
    console.log("🔄 Initializing database schema...");
    await vercelDb.initializeSchema();

    // Create demo data for development
    if (process.env.NODE_ENV === "development") {
      console.log("🌱 Creating demo data for development...");
      await createDemoData();
    }

    // Display database stats
    console.log("📊 Database Statistics:");
    const stats = await vercelDb.getDatabaseStats();
    console.log(`  - Users: ${stats.totalUsers}`);
    console.log(`  - Conversations: ${stats.totalConversations}`);
    console.log(`  - Messages: ${stats.totalMessages}`);
    console.log(
      `  - Avg Messages/Conversation: ${stats.avgMessagesPerConversation.toFixed(
        1
      )}`
    );

    console.log("✅ Vercel Postgres setup completed successfully!");
  } catch (error) {
    console.error("❌ Database setup failed:", error);
    process.exit(1);
  }
}

async function createDemoData() {
  try {
    // Create demo user
    const demoUser = await vercelDb.createUser({
      email: "<EMAIL>",
      name: "Demo User",
      avatar: null,
    });

    console.log(`✅ Demo user created: ${demoUser.id}`);

    // Create demo conversation
    const demoConversation = await vercelDb.createConversation({
      userId: demoUser.id,
      title: "Welcome to Spark AI",
    });

    console.log(`✅ Demo conversation created: ${demoConversation.id}`);

    // Create demo messages
    await vercelDb.createMessage({
      conversationId: demoConversation.id,
      userId: demoUser.id,
      role: "assistant",
      content:
        "Hello! Welcome to Spark AI Experience. I'm here to help you with any questions or tasks you might have. How can I assist you today?",
      metadata: {
        model: "glm-4-flash",
        tokens: 32,
        temperature: 0.7,
      },
    });

    await vercelDb.createMessage({
      conversationId: demoConversation.id,
      userId: demoUser.id,
      role: "user",
      content: "Hi! Can you tell me about your capabilities?",
      metadata: {
        tokens: 10,
      },
    });

    await vercelDb.createMessage({
      conversationId: demoConversation.id,
      userId: demoUser.id,
      role: "assistant",
      content:
        "I'm Spark AI, powered by advanced language models. I can help you with:\n\n• Answering questions and providing information\n• Creative writing and content generation\n• Code analysis and programming help\n• Problem-solving and brainstorming\n• Web search for current information\n• And much more!\n\nFeel free to ask me anything!",
      metadata: {
        model: "glm-4-flash",
        tokens: 78,
        temperature: 0.7,
        features: ["text-generation", "web-search"],
      },
    });

    console.log("✅ Demo messages created");

    // Log demo API usage
    await vercelDb.logApiUsage({
      userId: demoUser.id,
      model: "glm-4-flash",
      tokens: 110,
      cost: 0.0011,
      endpoint: "/api/chat",
      status: "success",
    });

    console.log("✅ Demo API usage logged");
  } catch (error) {
    console.error("❌ Error creating demo data:", error);
    throw error;
  }
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupVercelDatabase();
}

export { setupVercelDatabase, createDemoData };
