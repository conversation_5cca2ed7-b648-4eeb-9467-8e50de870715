#!/usr/bin/env tsx

/**
 * Database Setup Script
 * Sets up Vercel Postgres database with initial schema and data
 */

import { PrismaClient } from '@prisma/client';
import { environmentManager } from '../src/config/environment';

const prisma = new PrismaClient();

async function setupDatabase() {
  console.log('🚀 Setting up Spark AI Experience database...');
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');

    // Run Prisma migrations
    console.log('🔄 Running database migrations...');
    // Note: In production, this would be handled by Vercel's build process
    
    // Seed initial data
    console.log('🌱 Seeding initial data...');
    await seedDatabase();
    
    console.log('✅ Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function seedDatabase() {
  // Create system configuration
  await prisma.systemConfig.upsert({
    where: { key: 'app_version' },
    update: { value: '1.0.0' },
    create: { key: 'app_version', value: '1.0.0' }
  });

  await prisma.systemConfig.upsert({
    where: { key: 'maintenance_mode' },
    update: { value: 'false' },
    create: { key: 'maintenance_mode', value: 'false' }
  });

  await prisma.systemConfig.upsert({
    where: { key: 'max_conversations_per_user' },
    update: { value: '100' },
    create: { key: 'max_conversations_per_user', value: '100' }
  });

  await prisma.systemConfig.upsert({
    where: { key: 'max_messages_per_conversation' },
    update: { value: '1000' },
    create: { key: 'max_messages_per_conversation', value: '1000' }
  });

  console.log('✅ Initial system configuration created');

  // Create a demo user for testing (only in development)
  if (environmentManager.isDevelopment()) {
    const demoUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Demo User',
        avatar: null
      }
    });

    // Create a demo conversation
    const demoConversation = await prisma.conversation.upsert({
      where: { id: 'demo-conversation' },
      update: {},
      create: {
        id: 'demo-conversation',
        title: 'Welcome to Spark AI',
        userId: demoUser.id
      }
    });

    // Create demo messages
    await prisma.message.upsert({
      where: { id: 'demo-message-1' },
      update: {},
      create: {
        id: 'demo-message-1',
        content: 'Hello! Welcome to Spark AI Experience. How can I help you today?',
        role: 'assistant',
        conversationId: demoConversation.id,
        userId: demoUser.id,
        metadata: {
          model: 'glm-4-flash',
          tokens: 15
        }
      }
    });

    console.log('✅ Demo data created for development');
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupDatabase();
}

export { setupDatabase, seedDatabase };