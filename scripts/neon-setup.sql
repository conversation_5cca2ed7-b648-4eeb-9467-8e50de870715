-- Neon Database Setup SQL
-- Run this in the Neon Console SQL Editor
-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE,
  name VA<PERSON>HA<PERSON>(255),
  avatar TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(500),
  is_archived B<PERSON>OLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE
  SET NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create api_usage table
CREATE TABLE IF NOT EXISTS api_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE
  SET NULL,
    model VARCHAR(100) NOT NULL,
    tokens INTEGER NOT NULL,
    cost DECIMAL(10, 6),
    endpoint VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
    error_code VARCHAR(50),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create system_config table
CREATE TABLE IF NOT EXISTS system_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key VARCHAR(255) UNIQUE NOT NULL,
  value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at ASC);
CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);
-- Seed system configuration
INSERT INTO system_config (key, value)
VALUES ('app_version', '1.0.0'),
  ('maintenance_mode', 'false'),
  ('max_conversations_per_user', '100'),
  ('max_messages_per_conversation', '1000'),
  ('api_rate_limit', '60'),
  ('max_tokens_per_request', '4000') ON CONFLICT (key) DO NOTHING;
-- Create demo user and conversation
INSERT INTO users (email, name)
VALUES ('<EMAIL>', 'Demo User') ON CONFLICT (email) DO
UPDATE
SET name = EXCLUDED.name;
-- Get the demo user ID and create a conversation
WITH demo_user AS (
  SELECT id
  FROM users
  WHERE email = '<EMAIL>'
),
demo_conversation AS (
  INSERT INTO conversations (user_id, title)
  SELECT id,
    'Welcome to Spark AI'
  FROM demo_user
  RETURNING id,
    user_id
)
INSERT INTO messages (
    conversation_id,
    user_id,
    role,
    content,
    metadata
  )
SELECT dc.id,
  dc.user_id,
  'assistant',
  'Hello! Welcome to Spark AI Experience. I''m here to help you with any questions or tasks you might have. How can I assist you today?',
  '{"model": "glm-4-flash", "tokens": 32, "temperature": 0.7}'::jsonb
FROM demo_conversation dc;
-- Show final statistics
SELECT 'Setup Complete!' as status,
  (
    SELECT COUNT(*)
    FROM users
  ) as total_users,
  (
    SELECT COUNT(*)
    FROM conversations
  ) as total_conversations,
  (
    SELECT COUNT(*)
    FROM messages
  ) as total_messages,
  (
    SELECT COUNT(*)
    FROM system_config
  ) as config_entries;