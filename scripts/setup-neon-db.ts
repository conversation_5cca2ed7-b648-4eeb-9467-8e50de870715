#!/usr/bin/env tsx

/**
 * Neon Database Setup Script
 * Simple setup for Neon Postgres database
 */

import { sql } from '@vercel/postgres';

async function setupNeonDatabase() {
  console.log('🚀 Setting up Neon Database...');
  
  try {
    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await sql`SELECT NOW() as current_time, version() as version`;
    console.log('✅ Database connected successfully');
    console.log(`⏰ Current time: ${testResult.rows[0].current_time}`);
    
    // Create tables
    console.log('🔄 Creating database schema...');
    
    // Users table
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE,
        name VARCHAR(255),
        avatar TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    console.log('✅ Users table created');

    // Conversations table
    await sql`
      CREATE TABLE IF NOT EXISTS conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(500),
        is_archived BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    console.log('✅ Conversations table created');

    // Messages table
    await sql`
      CREATE TABLE IF NOT EXISTS messages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    console.log('✅ Messages table created');

    // API Usage table
    await sql`
      CREATE TABLE IF NOT EXISTS api_usage (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        model VARCHAR(100) NOT NULL,
        tokens INTEGER NOT NULL,
        cost DECIMAL(10,6),
        endpoint VARCHAR(255) NOT NULL,
        status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
        error_code VARCHAR(50),
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    console.log('✅ API Usage table created');

    // System Config table
    await sql`
      CREATE TABLE IF NOT EXISTS system_config (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        key VARCHAR(255) UNIQUE NOT NULL,
        value TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    console.log('✅ System Config table created');

    // Create indexes
    console.log('🔄 Creating indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);`;
    await sql`CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);`;
    await sql`CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at ASC);`;
    await sql`CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage(user_id);`;
    console.log('✅ Indexes created');

    // Seed system config
    console.log('🌱 Seeding system configuration...');
    await sql`
      INSERT INTO system_config (key, value)
      VALUES ('app_version', '1.0.0')
      ON CONFLICT (key) DO NOTHING;
    `;
    await sql`
      INSERT INTO system_config (key, value)
      VALUES ('maintenance_mode', 'false')
      ON CONFLICT (key) DO NOTHING;
    `;
    console.log('✅ System configuration seeded');

    // Create demo user for development
    if (process.env.NODE_ENV === 'development') {
      console.log('🌱 Creating demo data...');
      
      const demoUser = await sql`
        INSERT INTO users (email, name)
        VALUES ('<EMAIL>', 'Demo User')
        ON CONFLICT (email) DO UPDATE SET name = EXCLUDED.name
        RETURNING id;
      `;
      
      const userId = demoUser.rows[0].id;
      
      const demoConversation = await sql`
        INSERT INTO conversations (user_id, title)
        VALUES (${userId}, 'Welcome to Spark AI')
        RETURNING id;
      `;
      
      const conversationId = demoConversation.rows[0].id;
      
      await sql`
        INSERT INTO messages (conversation_id, user_id, role, content, metadata)
        VALUES (
          ${conversationId}, 
          ${userId}, 
          'assistant', 
          'Hello! Welcome to Spark AI Experience. How can I help you today?',
          '{"model": "glm-4-flash", "tokens": 15}'
        );
      `;
      
      console.log('✅ Demo data created');
    }

    // Get final stats
    const stats = await sql`
      SELECT 
        (SELECT COUNT(*) FROM users) as users,
        (SELECT COUNT(*) FROM conversations) as conversations,
        (SELECT COUNT(*) FROM messages) as messages;
    `;
    
    console.log('📊 Database Statistics:');
    console.log(`  - Users: ${stats.rows[0].users}`);
    console.log(`  - Conversations: ${stats.rows[0].conversations}`);
    console.log(`  - Messages: ${stats.rows[0].messages}`);
    
    console.log('✅ Neon database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run setup
setupNeonDatabase();