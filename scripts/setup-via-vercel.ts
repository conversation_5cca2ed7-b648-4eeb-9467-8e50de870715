#!/usr/bin/env tsx

/**
 * Setup Database via Vercel Environment
 * Uses Vercel's environment variables to set up the database
 */

console.log('🚀 Setting up database via Vercel...');
console.log('📋 Available Environment Variables:');

// Show available database environment variables
const dbVars = [
  'DATABASE_URL',
  'POSTGRES_URL', 
  'POSTGRES_PRISMA_URL',
  'DATABASE_URL_UNPOOLED',
  'POSTGRES_URL_NON_POOLING',
  'POSTGRES_HOST',
  'POSTGRES_USER',
  'POSTGRES_DATABASE'
];

dbVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: Not set`);
  }
});

console.log('\n🎯 Next Steps:');
console.log('1. Go to your Vercel Dashboard');
console.log('2. Storage Tab → spark-ai-db');
console.log('3. Click "Open in Neon" or "SQL Editor"');
console.log('4. Run the SQL script from scripts/neon-setup.sql');
console.log('\n✨ Your database is ready to use!');