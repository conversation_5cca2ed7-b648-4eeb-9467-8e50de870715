#!/usr/bin/env tsx

/**
 * Vercel Postgres Setup Script
 * Initializes database schema and seeds initial data
 */

import { vercelDb } from '../src/lib/database-integration';
import { environmentManager } from '../src/config/environment';

async function setupVercelPostgres() {
  console.log('🚀 Setting up Vercel Postgres for Spark AI Experience...');
  
  try {
    // Check database connection
    console.log('📡 Testing database connection...');
    const health = await vercelDb.healthCheck();
    
    if (!health.connected) {
      throw new Error(`Database connection failed: ${health.error}`);
    }
    
    console.log(`✅ Database connected (${health.latency}ms latency)`);
    
    // Initialize schema
    console.log('🔄 Initializing database schema...');
    await vercelDb.initializeSchema();
    
    // Create demo data for development
    if (environmentManager.isDevelopment()) {
      console.log('🌱 Creating demo data...');
      await createDemoData();
    }
    
    // Display stats
    const stats = await vercelDb.getDatabaseStats();
    console.log('📊 Database Statistics:', stats);
    
    console.log('✅ Vercel Postgres setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

async function createDemoData() {
  // Create demo user
  const demoUser = await vercelDb.createUser({
    email: '<EMAIL>',
    name: 'Demo User',
    avatar: null
  });
  
  // Create demo conversation
  const conversation = await vercelDb.createConversation({
    userId: demoUser.id,
    title: 'Welcome to Spark AI'
  });
  
  // Create demo messages
  await vercelDb.createMessage({
    conversationId: conversation.id,
    userId: demoUser.id,
    role: 'assistant',
    content: 'Hello! Welcome to Spark AI Experience. How can I help you today?',
    metadata: { model: 'glm-4-flash', tokens: 15 }
  });
  
  console.log('✅ Demo data created');
}

if (require.main === module) {
  setupVercelPostgres();
}

export { setupVercelPostgres };