# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Database
*.db
*.sqlite

# OS generated files
Thumbs.db
.DS_Store

# IDE
.vscode/
.idea/

# Temporary files
*.tmp
*.temp

# Build artifacts
build/
out/

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# Testing
coverage/
.nyc_output/

# Cache
.cache/
.parcel-cache/

# Misc
*.tgz
*.tar.gz