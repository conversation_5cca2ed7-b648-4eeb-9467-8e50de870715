# Development Environment Configuration
# This file contains development-specific environment variables

# Environment
NODE_ENV=development
VITE_ENV=development

# Z.AI API Configuration
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_API_KEY=9dbec631df674f24a9d24149ca9ff0b7.KM2cUiTNP5pfNS0m
VITE_API_VERSION=v4
VITE_API_TIMEOUT=30000

# Model Configuration for Z.AI
VITE_DEFAULT_MODEL=glm-4-flash
VITE_MAX_TOKENS=4000
VITE_TEMPERATURE=0.7

# Video Generation Configuration
VITE_VIDEO_MODEL=cogvideox-3
VITE_VIDEO_MAX_WIDTH=1920
VITE_VIDEO_MAX_HEIGHT=1080
VITE_VIDEO_MAX_DURATION=30
VITE_VIDEO_MAX_STEPS=100

# Retry Configuration
VITE_MAX_RETRIES=3
VITE_RETRY_DELAY=2000
VITE_EXPONENTIAL_BACKOFF=true

# Debug and Monitoring (enabled in development)
VITE_DEBUG_MODE=true
VITE_ANALYTICS_ENABLED=false
VITE_ERROR_TRACKING_ENABLED=false
VITE_PERFORMANCE_MONITORING=false

# Security Configuration (relaxed for development)
VITE_RATE_LIMIT_ENABLED=false
VITE_RATE_LIMIT_MAX_REQUESTS=1000
VITE_RATE_LIMIT_WINDOW_MS=60000
VITE_MAX_INPUT_LENGTH=50000
VITE_LOG_SECURITY_EVENTS=true
VITE_CORS_ORIGINS=localhost,127.0.0.1,0.0.0.0

# Database Configuration (local development)
POSTGRES_URL=postgresql://localhost:5432/spark_ai_dev
POSTGRES_PRISMA_URL=postgresql://localhost:5432/spark_ai_dev
POSTGRES_URL_NON_POOLING=postgresql://localhost:5432/spark_ai_dev
POSTGRES_USER=postgres
POSTGRES_HOST=localhost
POSTGRES_PASSWORD=
POSTGRES_DATABASE=spark_ai_dev

# Development-specific features
VITE_MOCK_API_ENABLED=true
VITE_HOT_RELOAD=true
VITE_SOURCE_MAPS=true