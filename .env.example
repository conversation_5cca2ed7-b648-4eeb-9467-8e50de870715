# Environment Configuration
NODE_ENV=development
VITE_ENV=development

# Z.AI API Configuration
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_API_KEY=your_zai_api_key_here
VITE_API_VERSION=v4
VITE_API_TIMEOUT=30000

# Model Configuration for Z.AI
VITE_DEFAULT_MODEL=glm-4
VITE_MAX_TOKENS=4000
VITE_TEMPERATURE=0.7

# Video Generation Configuration
VITE_VIDEO_MODEL=cogvideox-3
VITE_VIDEO_MAX_WIDTH=1920
VITE_VIDEO_MAX_HEIGHT=1080
VITE_VIDEO_MAX_DURATION=30
VITE_VIDEO_MAX_STEPS=100

# Retry Configuration
VITE_MAX_RETRIES=3
VITE_RETRY_DELAY=2000
VITE_EXPONENTIAL_BACKOFF=true

# Debug and Monitoring Configuration
VITE_DEBUG_MODE=false
VITE_ANALYTICS_ENABLED=true
VITE_ERROR_TRACKING_ENABLED=true
VITE_PERFORMANCE_MONITORING=true

# Security Configuration
VITE_RATE_LIMIT_ENABLED=true
VITE_RATE_LIMIT_MAX_REQUESTS=60
VITE_RATE_LIMIT_WINDOW_MS=60000
VITE_MAX_INPUT_LENGTH=10000
VITE_LOG_SECURITY_EVENTS=true
VITE_CORS_ORIGINS=localhost,127.0.0.1

# Database Configuration (for Vercel Postgres)
POSTGRES_URL=your_postgres_connection_string
POSTGRES_PRISMA_URL=your_postgres_prisma_url
POSTGRES_URL_NON_POOLING=your_postgres_non_pooling_url
POSTGRES_USER=your_postgres_user
POSTGRES_HOST=your_postgres_host
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DATABASE=your_postgres_database

# Feature Flags
VITE_MOCK_API_ENABLED=false
VITE_FEATURE_FLAGS_ENABLED=false
VITE_A_B_TESTING_ENABLED=false