# Deployment Guide - Vercel

This guide covers the deployment process for the Spark AI Experience application on Vercel.

## Pre-Deployment Checklist

### ✅ Configuration Verification

- [x] **vercel.json optimized** - Enhanced with security headers, CORS, and caching
- [x] **Security headers configured** - CSP, HSTS, X-Frame-Options, etc.
- [x] **CORS configuration implemented** - Proper origin validation for Z.AI API
- [x] **Build process tested** - Local build completes successfully
- [x] **Environment variables documented** - Complete list in ENVIRONMENT.md

### 🔧 Build Configuration

- **Framework**: Vite + React + TypeScript
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Node Version**: 18.x (recommended)

### 🔒 Security Features

- **Content Security Policy**: Configured for Z.AI API access
- **CORS Headers**: Properly configured for API routes
- **Rate Limiting**: Implemented in application layer
- **Input Sanitization**: Active for all user inputs
- **API Key Validation**: Format validation and secure storage

## Deployment Steps

### 1. Environment Variables Setup

In your Vercel dashboard, configure these environment variables:

#### Required Variables
```bash
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_API_KEY=your_api_key_here
VITE_DEFAULT_MODEL=glm-4-flash
```

#### Database Variables (Vercel Postgres)
```bash
POSTGRES_URL=postgresql://...
POSTGRES_PRISMA_URL=postgresql://...
POSTGRES_URL_NON_POOLING=postgresql://...
```

#### Optional Configuration
```bash
VITE_DEBUG_MODE=false
VITE_RATE_LIMIT_ENABLED=true
VITE_MAX_TOKENS=4000
VITE_TEMPERATURE=0.7
```

### 2. Deploy to Vercel

#### Option A: GitHub Integration (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure auto-deployment on push to main branch
3. Set up preview deployments for pull requests

#### Option B: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

### 3. Post-Deployment Verification

#### Test Core Functionality
- [ ] Application loads without errors
- [ ] Chat interface is responsive
- [ ] Z.AI API integration works
- [ ] Error handling functions properly
- [ ] Security headers are present

#### Performance Checks
- [ ] Page load time < 3 seconds
- [ ] Bundle size is optimized
- [ ] Images are properly compressed
- [ ] Caching headers are set

#### Security Verification
```bash
# Check security headers
curl -I https://your-app.vercel.app

# Verify CORS configuration
curl -H "Origin: https://your-domain.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS https://your-app.vercel.app/api/test
```

## Configuration Details

### vercel.json Features

#### Security Headers
- **CSP**: Restricts resource loading to trusted sources
- **HSTS**: Enforces HTTPS connections
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing

#### CORS Configuration
- **Origin Validation**: Only allows specified domains
- **Credentials Support**: Enabled for authenticated requests
- **Preflight Caching**: 24-hour cache for OPTIONS requests

#### Performance Optimization
- **Static Asset Caching**: 1-year cache for immutable assets
- **Compression**: Automatic gzip compression
- **CDN**: Global edge network distribution

### Build Optimization

#### Bundle Analysis
Current build size: ~696KB (217KB gzipped)

#### Recommendations
- Consider code splitting for large components
- Implement lazy loading for non-critical features
- Optimize images and assets

## Monitoring & Maintenance

### Vercel Analytics
- Enable Vercel Analytics for performance monitoring
- Set up alerts for errors and performance issues
- Monitor API usage and rate limits

### Error Tracking
- Application includes comprehensive error handling
- Security events are logged (when enabled)
- Circuit breaker pattern prevents cascade failures

### Regular Maintenance
- Update dependencies regularly
- Rotate API keys periodically
- Monitor security advisories
- Review and update CORS origins

## Troubleshooting

### Common Issues

#### Build Failures
- Check Node.js version compatibility
- Verify all dependencies are installed
- Review build logs for specific errors

#### API Connection Issues
- Verify API key format and validity
- Check CORS configuration
- Ensure environment variables are set correctly

#### Performance Issues
- Enable debug mode to identify bottlenecks
- Check network requests in browser dev tools
- Monitor Vercel function execution times

### Debug Mode
Enable debug mode for detailed logging:
```bash
VITE_DEBUG_MODE=true
```

This provides:
- Detailed API request/response logging
- Configuration validation results
- Performance metrics
- Security event logging

## Production Best Practices

### Security
- Never expose API keys in client-side code
- Regularly audit dependencies for vulnerabilities
- Monitor for unusual API usage patterns
- Keep security headers up to date

### Performance
- Monitor Core Web Vitals
- Optimize images and assets
- Use appropriate caching strategies
- Consider implementing service worker for offline support

### Reliability
- Implement proper error boundaries
- Use circuit breaker pattern for external APIs
- Set up monitoring and alerting
- Plan for graceful degradation

## Support

For deployment issues:
1. Check Vercel deployment logs
2. Review browser console for client-side errors
3. Verify environment variable configuration
4. Test API connectivity independently

For application issues:
1. Enable debug mode for detailed logging
2. Check security event logs
3. Verify API key and endpoint configuration
4. Test with mock responses to isolate issues