import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock environment
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://test.api.com',
  VITE_DEBUG_MODE: 'false',
};

vi.stubGlobal('import.meta', { env: mockEnv });

// Mock fetch
global.fetch = vi.fn();

describe('Basic Functionality Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('API Service Basic Tests', () => {
    it('should handle successful API responses', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-123',
          choices: [{
            message: {
              content: 'Test response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      // Import after mocking
      const { apiService } = await import('../services/apiService');
      
      const result = await apiService.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Test response');
    });

    it('should handle API errors with fallback', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: () => Promise.resolve('Server error'),
        headers: new Map([['content-type', 'text/plain']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      const result = await apiService.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should handle network errors with fallback', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const { apiService } = await import('../services/apiService');
      
      const result = await apiService.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should test connection successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'ping'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      const result = await apiService.testConnection();

      expect(result.connected).toBe(true);
      expect(result.details?.status).toBe(200);
      expect(result.details?.responseTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle connection test failures', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Connection failed'));

      const { apiService } = await import('../services/apiService');
      
      const result = await apiService.testConnection();

      expect(result.connected).toBe(false);
      expect(result.error).toBe('Connection failed');
    });
  });

  describe('Security Manager Basic Tests', () => {
    it('should sanitize malicious input', async () => {
      const { securityManager } = await import('../config/security');
      
      const maliciousInput = '<script>alert("xss")</script>Hello';
      const sanitized = securityManager.sanitizeInput(maliciousInput);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Hello');
    });

    it('should validate API keys', async () => {
      const { securityManager } = await import('../config/security');
      
      const validKey = 'a'.repeat(32) + '.' + 'b'.repeat(16);
      const invalidKey = 'invalid-key';
      
      expect(securityManager.validateApiKey(validKey)).toBe(true);
      expect(securityManager.validateApiKey(invalidKey)).toBe(false);
    });

    it('should handle rate limiting', async () => {
      const { securityManager } = await import('../config/security');
      
      // Set test configuration
      securityManager.setTestConfig({
        rateLimiting: {
          enabled: true,
          maxRequests: 2,
          windowMs: 60000,
          skipSuccessfulRequests: false
        }
      });

      const clientId = 'test-client';
      
      // First two requests should be allowed
      expect(securityManager.checkRateLimit(clientId).allowed).toBe(true);
      expect(securityManager.checkRateLimit(clientId).allowed).toBe(true);
      
      // Third request should be blocked
      expect(securityManager.checkRateLimit(clientId).allowed).toBe(false);
    });
  });

  describe('Performance Tests', () => {
    it('should complete requests within reasonable time', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Fast response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      const startTime = performance.now();
      const result = await apiService.sendMessage([
        { role: 'user', content: 'Test' }
      ]);
      const endTime = performance.now();
      
      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Concurrent response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      const promises = Array.from({ length: 3 }, (_, i) =>
        apiService.sendMessage([
          { role: 'user', content: `Message ${i}` }
        ], { clientId: `client-${i}` })
      );

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete chat flow', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chat-123',
          choices: [{
            message: {
              content: 'Hello! How can I help you?'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      // Test multi-turn conversation
      const messages = [
        { role: 'user' as const, content: 'Hello' },
        { role: 'assistant' as const, content: 'Hi there!' },
        { role: 'user' as const, content: 'How are you?' }
      ];

      const result = await apiService.sendMessage(messages);
      
      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Hello! How can I help you?');
    });

    it('should enable features based on query content', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Current information response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const { apiService } = await import('../services/apiService');
      
      // Test web search activation
      const result = await apiService.sendMessage([
        { role: 'user', content: 'Was sind die aktuellen Nachrichten heute?' }
      ], {
        useWebSearch: true
      });

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('web_search')
        })
      );
    });
  });
});