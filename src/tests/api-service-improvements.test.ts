import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock environment for testing
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://test.api.com',
  VITE_DEBUG_MODE: 'true',
};

vi.stubGlobal('import.meta', { env: mockEnv });

import { apiService } from '../services/apiService';
import { apiConfig } from '../config/api';

// Mock fetch for testing
global.fetch = vi.fn();

describe('API Service Improvements', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set test environment
    process.env.NODE_ENV = 'test';
    // Configure API for testing
    apiConfig.updateConfig({
      apiKey: 'test-************************************.1234567890123456',
      endpoint: 'https://test.api.com',
      debugMode: false // Disable debug mode for cleaner test output
    });
  });

  describe('Enhanced Error Handling', () => {
    it('should categorize errors correctly', async () => {
      // Mock a network error
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const result = await apiService.sendMessage([
        { role: 'user', content: 'test message' }
      ]);

      // With network errors, the service should use fallback
      if (result.success) {
        // Fallback response should contain fallback indicator
        expect(
          result.data?.message?.includes('Fallback Mode Active') || 
          result.data?.message?.includes('Service Recovery Mode')
        ).toBe(true);
      } else {
        expect(result.error?.code).toBe('NETWORK_ERROR');
        expect(result.error?.details?.recoveryActions).toBeDefined();
      }
    });

    it('should provide recovery actions for different error types', async () => {
      // Mock a 429 rate limit error
      (global.fetch as any).mockResolvedValue({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: () => Promise.resolve('Rate limit exceeded'),
        headers: new Map([['content-type', 'application/json']])
      });

      const result = await apiService.sendMessage([
        { role: 'user', content: 'test message' }
      ]);

      // Rate limit errors should trigger fallback
      if (result.success) {
        expect(
          result.data?.message?.includes('Fallback Mode Active') || 
          result.data?.message?.includes('Service Recovery Mode')
        ).toBe(true);
      } else {
        expect(result.error?.code).toBe('RATE_LIMIT_ERROR');
        expect(result.error?.details?.recoveryActions).toContain('Wait a few minutes before trying again');
      }
    });
  });

  describe('Circuit Breaker', () => {
    it('should track circuit breaker status', () => {
      const status = apiService.getCircuitBreakerStatus();
      
      expect(status).toHaveProperty('state');
      expect(status).toHaveProperty('failures');
      expect(status).toHaveProperty('healthy');
      expect(['CLOSED', 'OPEN', 'HALF_OPEN']).toContain(status.state);
    });
  });

  describe('Health Check', () => {
    it('should perform comprehensive health check', async () => {
      // Mock successful API response
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'test response' } }]
        }),
        headers: new Map([['content-type', 'application/json']])
      });

      const healthResult = await apiService.healthCheck();
      
      expect(healthResult).toHaveProperty('healthy');
      expect(healthResult).toHaveProperty('checks');
      expect(healthResult.checks).toHaveProperty('connection');
      expect(healthResult.checks).toHaveProperty('authentication');
      expect(healthResult.checks).toHaveProperty('rateLimit');
      expect(healthResult.checks).toHaveProperty('configuration');
    });
  });

  describe('Enhanced Connection Test', () => {
    it('should provide detailed connection test results', async () => {
      // Mock successful API response
      (global.fetch as any).mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'ping' } }]
        }),
        headers: new Map([['content-type', 'application/json']])
      });

      const connectionResult = await apiService.testConnection();
      
      expect(connectionResult.connected).toBe(true);
      expect(connectionResult.details).toBeDefined();
      expect(connectionResult.details?.responseTime).toBeGreaterThanOrEqual(0);
      expect(connectionResult.details?.status).toBe(200);
    });

    it('should handle connection failures gracefully', async () => {
      // Mock connection failure
      (global.fetch as any).mockRejectedValue(new Error('Connection failed'));

      const connectionResult = await apiService.testConnection();
      
      expect(connectionResult.connected).toBe(false);
      expect(connectionResult.error).toBeDefined();
      expect(connectionResult.details).toBeDefined();
      expect(connectionResult.details?.responseTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Fallback Strategies', () => {
    it('should use fallback for network errors', async () => {
      // Mock network error
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const result = await apiService.sendMessage([
        { role: 'user', content: 'test message' }
      ]);

      // Should get a fallback response instead of just an error
      expect(result.success).toBe(true);
      // Should contain either fallback indicator
      expect(
        result.data?.message?.includes('Fallback Mode Active') || 
        result.data?.message?.includes('Service Recovery Mode')
      ).toBe(true);
    });
  });

  describe('Enhanced Logging', () => {
    it('should log detailed error information', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock API error
      (global.fetch as any).mockRejectedValue(new Error('Test error'));

      await apiService.sendMessage([
        { role: 'user', content: 'test message' }
      ]);

      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });
});