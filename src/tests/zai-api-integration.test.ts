import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock environment for testing
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://open.bigmodel.cn/api/paas/v4',
  VITE_DEBUG_MODE: 'false',
  VITE_DEFAULT_MODEL: 'glm-4-plus',
};

vi.stubGlobal('import.meta', { env: mockEnv });

import { apiService, type ChatMessage, type EnhancedGLMRequest } from '../services/apiService';
import { apiConfig } from '../config/api';

// Mock fetch for integration tests
global.fetch = vi.fn();

describe('Z.AI API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Configure API for testing
    apiConfig.updateConfig({
      apiKey: 'test-************************************.1234567890123456',
      endpoint: 'https://open.bigmodel.cn/api/paas/v4',
      debugMode: false,
      defaultModel: 'glm-4-plus',
      maxTokens: 4000,
      temperature: 0.7
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Chat Completion', () => {
    it('should successfully complete a basic chat request', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chatcmpl-123',
          object: 'chat.completion',
          created: 1677652288,
          model: 'glm-4-plus',
          choices: [{
            index: 0,
            message: {
              role: 'assistant',
              content: 'Hello! I\'m Spark AI, your advanced AI assistant. How can I help you today?'
            },
            finish_reason: 'stop'
          }],
          usage: {
            prompt_tokens: 10,
            completion_tokens: 20,
            total_tokens: 30
          }
        }),
        headers: new Map([
          ['content-type', 'application/json'],
          ['x-request-id', 'req-123']
        ])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello, who are you?' }
      ];

      const result = await apiService.sendMessage(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Hello! I\'m Spark AI, your advanced AI assistant. How can I help you today?');
      expect(result.data?.id).toBe('chatcmpl-123');

      // Verify the request was made correctly
      expect(global.fetch).toHaveBeenCalledWith(
        'https://open.bigmodel.cn/api/paas/v4/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-************************************.1234567890123456'
          }),
          body: expect.stringContaining('glm-4-plus')
        })
      );
    });

    it('should handle multi-turn conversations', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chatcmpl-456',
          choices: [{
            message: {
              content: 'Based on our previous conversation about AI, I can provide more specific information about machine learning algorithms.'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Tell me about AI' },
        { role: 'assistant', content: 'AI is artificial intelligence...' },
        { role: 'user', content: 'Can you be more specific about machine learning?' }
      ];

      const result = await apiService.sendMessage(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('machine learning algorithms');

      // Verify all messages were sent in the request
      const fetchCall = (global.fetch as any).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      expect(requestBody.messages).toHaveLength(4); // Including system message
    });
  });

  describe('Enhanced GLM-4.5 Features', () => {
    it('should send enhanced GLM request with web search', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chatcmpl-web-search',
          choices: [{
            message: {
              content: 'Based on my web search, here are the latest developments in AI technology...'
            }
          }],
          usage: {
            prompt_tokens: 50,
            completion_tokens: 100,
            total_tokens: 150
          }
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const request: EnhancedGLMRequest = {
        messages: [
          { role: 'user', content: 'What are the latest AI developments in 2024?' }
        ],
        tools: [
          {
            type: 'web_search',
            web_search: {
              enable: true
            }
          }
        ],
        tool_choice: 'auto',
        temperature: 0.7,
        max_tokens: 4000
      };

      const result = await apiService.sendEnhancedGLMRequest(request);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('web search');
      expect(result.data?.usage?.total_tokens).toBe(150);

      // Verify web search tool was included in request
      const fetchCall = (global.fetch as any).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      expect(requestBody.tools).toContainEqual(
        expect.objectContaining({
          type: 'web_search',
          web_search: { enable: true }
        })
      );
    });

    it('should send enhanced GLM request with deep thinking', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chatcmpl-thinking',
          choices: [{
            message: {
              content: 'After careful analysis and deep thinking, I can conclude that...'
            }
          }],
          thinking_content: 'Let me think about this step by step...',
          usage: {
            prompt_tokens: 100,
            completion_tokens: 200,
            total_tokens: 300
          }
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const request: EnhancedGLMRequest = {
        messages: [
          { role: 'user', content: 'Analyze the complex relationship between quantum computing and AI' }
        ],
        thinking: {
          type: 'enabled',
          budget_tokens: 2000
        },
        temperature: 0.7,
        max_tokens: 4000
      };

      const result = await apiService.sendEnhancedGLMRequest(request);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('deep thinking');
      expect(result.data?.thinking_content).toBe('Let me think about this step by step...');

      // Verify thinking was enabled in request
      const fetchCall = (global.fetch as any).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      expect(requestBody.thinking).toEqual({
        type: 'enabled',
        budget_tokens: 2000
      });
    });

    it('should send enhanced GLM request with function calling', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'chatcmpl-function',
          choices: [{
            message: {
              content: 'The current time is 2024-01-15 14:30:00 CET',
              tool_calls: [{
                id: 'call_123',
                type: 'function',
                function: {
                  name: 'get_current_time',
                  arguments: '{"timezone": "Europe/Berlin"}'
                }
              }]
            }
          }],
          usage: {
            prompt_tokens: 75,
            completion_tokens: 50,
            total_tokens: 125
          }
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const request: EnhancedGLMRequest = {
        messages: [
          { role: 'user', content: 'What time is it now in Berlin?' }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'get_current_time',
              description: 'Get the current date and time',
              parameters: {
                type: 'object',
                properties: {
                  timezone: {
                    type: 'string',
                    description: 'Timezone for the time'
                  }
                }
              }
            }
          }
        ],
        tool_choice: 'auto'
      };

      const result = await apiService.sendEnhancedGLMRequest(request);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('current time');
      expect(result.data?.tool_calls).toHaveLength(1);
      expect(result.data?.tool_calls?.[0].function.name).toBe('get_current_time');
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle API rate limiting (429)', async () => {
      const mockResponse = {
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: () => Promise.resolve(JSON.stringify({
          error: {
            type: 'rate_limit_exceeded',
            message: 'Rate limit exceeded. Please try again later.',
            code: 'rate_limit_exceeded'
          }
        })),
        headers: new Map([
          ['content-type', 'application/json'],
          ['retry-after', '60']
        ])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response instead of error
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should handle API authentication errors (401)', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: () => Promise.resolve(JSON.stringify({
          error: {
            type: 'invalid_api_key',
            message: 'Invalid API key provided',
            code: 'invalid_api_key'
          }
        })),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should handle server errors (500)', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: () => Promise.resolve('Internal server error'),
        headers: new Map([['content-type', 'text/plain']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should handle network timeouts', async () => {
      (global.fetch as any).mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should retry failed requests with exponential backoff', async () => {
      let callCount = 0;
      (global.fetch as any).mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            id: 'chatcmpl-retry',
            choices: [{
              message: {
                content: 'Success after retry'
              }
            }]
          }),
          headers: new Map([['content-type', 'application/json']])
        });
      });

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test retry message' }
      ];

      const result = await apiService.sendMessage(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Success after retry');
      expect(callCount).toBe(3); // Should have retried twice
    });
  });

  describe('Connection Testing', () => {
    it('should successfully test API connection', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-connection',
          choices: [{
            message: {
              content: 'ping'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await apiService.testConnection();

      expect(result.connected).toBe(true);
      expect(result.details?.status).toBe(200);
      expect(result.details?.responseTime).toBeGreaterThanOrEqual(0);
      expect(result.error).toBeUndefined();
    });

    it('should detect connection failures', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Connection refused'));

      const result = await apiService.testConnection();

      expect(result.connected).toBe(false);
      expect(result.error).toBe('Connection refused');
      expect(result.details?.responseTime).toBeGreaterThanOrEqual(0);
    });

    it('should detect API key issues during connection test', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: () => Promise.resolve('Invalid API key'),
        headers: new Map([['content-type', 'text/plain']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await apiService.testConnection();

      expect(result.connected).toBe(false);
      expect(result.error).toContain('401');
      expect(result.details?.status).toBe(401);
    });
  });

  describe('Health Check', () => {
    it('should perform comprehensive health check', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'health-check',
          choices: [{
            message: {
              content: 'Health check successful'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await apiService.healthCheck();

      expect(result.healthy).toBe(true);
      expect(result.checks.connection.status).toBe('healthy');
      expect(result.checks.authentication.status).toBe('healthy');
      expect(result.checks.rateLimit.status).toBe('healthy');
      expect(result.checks.configuration.status).toBe('healthy');
    });

    it('should detect unhealthy API during health check', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Service unavailable'));

      const result = await apiService.healthCheck();

      expect(result.healthy).toBe(false);
      expect(result.checks.connection.status).toBe('unhealthy');
      expect(result.checks.connection.error).toBe('Service unavailable');
    });
  });

  describe('Streaming Responses', () => {
    it('should handle streaming responses correctly', async () => {
      const mockStreamData = [
        'data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" there!"}}]}\n\n',
        'data: [DONE]\n\n'
      ];

      const mockResponse = {
        ok: true,
        status: 200,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(mockStreamData[0])
              })
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(mockStreamData[1])
              })
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(mockStreamData[2])
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        },
        headers: new Map([['content-type', 'text/event-stream']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      let streamedContent = '';
      const streamCallback = {
        onToken: (token: string) => {
          streamedContent += token;
        },
        onComplete: (message: string) => {
          expect(message).toBe('Hello there!');
        },
        onError: (error: Error) => {
          throw error;
        }
      };

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const result = await apiService.sendMessage(messages, {
        stream: true,
        onStream: streamCallback
      });

      expect(result.success).toBe(true);
      expect(streamedContent).toBe('Hello there!');
    });

    it('should handle streaming errors gracefully', async () => {
      const mockStreamData = [
        'data: {"error":{"message":"Streaming error occurred"}}\n\n'
      ];

      const mockResponse = {
        ok: true,
        status: 200,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(mockStreamData[0])
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        },
        headers: new Map([['content-type', 'text/event-stream']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      let errorOccurred = false;
      const streamCallback = {
        onToken: (token: string) => {},
        onComplete: (message: string) => {},
        onError: (error: Error) => {
          errorOccurred = true;
          expect(error.message).toContain('Streaming error occurred');
        }
      };

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const result = await apiService.sendMessage(messages, {
        stream: true,
        onStream: streamCallback
      });

      expect(errorOccurred).toBe(true);
      expect(result.success).toBe(false);
    });
  });

  describe('Request Security and Headers', () => {
    it('should include proper security headers', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-security',
          choices: [{ message: { content: 'Response' } }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test security headers' }
      ];

      await apiService.sendMessage(messages);

      const fetchCall = (global.fetch as any).mock.calls[0];
      const headers = fetchCall[1].headers;

      expect(headers['Content-Type']).toBe('application/json');
      expect(headers['Authorization']).toBe('Bearer test-************************************.1234567890123456');
      expect(headers['X-Request-ID']).toBeDefined();
      expect(headers['X-Client-Version']).toBe('1.0.0');
      expect(headers['User-Agent']).toBe('Spark-AI-Experience/1.0.0');
      expect(headers['X-CSRF-Token']).toBeDefined();
    });

    it('should generate unique request IDs', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-request-id',
          choices: [{ message: { content: 'Response' } }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test request ID' }
      ];

      // Make two requests
      await apiService.sendMessage(messages);
      await apiService.sendMessage(messages);

      const firstCall = (global.fetch as any).mock.calls[0];
      const secondCall = (global.fetch as any).mock.calls[1];

      const firstRequestId = firstCall[1].headers['X-Request-ID'];
      const secondRequestId = secondCall[1].headers['X-Request-ID'];

      expect(firstRequestId).toBeDefined();
      expect(secondRequestId).toBeDefined();
      expect(firstRequestId).not.toBe(secondRequestId);
    });
  });
});