import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock environment
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://test.api.com',
  VITE_DEBUG_MODE: 'false',
};

vi.stubGlobal('import.meta', { env: mockEnv });

// Mock dependencies with realistic implementations
vi.mock('@/services/apiService', () => ({
  apiService: {
    sendMessage: vi.fn(),
    sendEnhancedGLMRequest: vi.fn(),
    testConnection: vi.fn(),
    getCircuitBreakerStatus: vi.fn(() => ({
      state: 'CLOSED',
      failures: 0,
      healthy: true
    })),
    getMockResponse: vi.fn(),
    healthCheck: vi.fn()
  }
}));

vi.mock('@/config/security', () => ({
  securityManager: {
    sanitizeInput: vi.fn((input) => input),
    checkRateLimit: vi.fn(() => ({ allowed: true })),
    generateSecureRequestId: vi.fn(() => 'test-request-id'),
    logSecurityEvent: vi.fn(),
    validateApiKey: vi.fn(() => true),
    resetRateLimits: vi.fn(),
    setTestConfig: vi.fn()
  }
}));

vi.mock('@/hooks/useNeuralTriggers', () => ({
  useNeuralTriggers: () => ({
    trackMessage: vi.fn(),
    getPersonalizedGreeting: vi.fn(() => 'Hallo!')
  })
}));

vi.mock('@/hooks/useAdaptiveUI', () => ({
  useAdaptiveUI: () => ({
    uiConfig: {
      animationsEnabled: true,
      animationSpeed: 'normal'
    },
    trackTyping: vi.fn(),
    trackInteraction: vi.fn(),
    trackFeatureUsage: vi.fn()
  })
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

import { EnhancedSparkChatInterface } from '../components/spark/EnhancedSparkChatInterface';
import { apiService } from '../services/apiService';
import { securityManager } from '../config/security';

describe('Chat Functionality End-to-End Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful connection test
    (apiService.testConnection as any).mockResolvedValue({
      connected: true,
      details: { status: 200, responseTime: 100 }
    });

    // Mock successful health check
    (apiService.healthCheck as any).mockResolvedValue({
      healthy: true,
      checks: {
        connection: { status: 'healthy' },
        authentication: { status: 'healthy' },
        rateLimit: { status: 'healthy' },
        configuration: { status: 'healthy' }
      }
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Complete Chat Flow', () => {
    it('should handle a complete conversation flow', async () => {
      // Mock API responses for a conversation
      (apiService.sendMessage as any)
        .mockResolvedValueOnce({
          success: true,
          data: {
            message: 'Hello! I\'m Spark AI. How can I help you today?',
            id: 'msg-1',
            timestamp: new Date()
          }
        })
        .mockResolvedValueOnce({
          success: true,
          data: {
            message: 'Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines that can perform tasks that typically require human intelligence.',
            id: 'msg-2',
            timestamp: new Date()
          }
        })
        .mockResolvedValueOnce({
          success: true,
          data: {
            message: 'Machine learning is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed.',
            id: 'msg-3',
            timestamp: new Date()
          }
        });

      render(<EnhancedSparkChatInterface />);

      // Wait for component to initialize
      await waitFor(() => {
        expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
      });

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      // First message
      await user.type(textarea, 'Hello, who are you?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Hello, who are you?')).toBeInTheDocument();
        expect(screen.getByText('Hello! I\'m Spark AI. How can I help you today?')).toBeInTheDocument();
      });

      // Second message
      await user.type(textarea, 'What is artificial intelligence?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('What is artificial intelligence?')).toBeInTheDocument();
        expect(screen.getByText(/Artificial Intelligence \(AI\) is a branch/)).toBeInTheDocument();
      });

      // Third message
      await user.type(textarea, 'Tell me about machine learning');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Tell me about machine learning')).toBeInTheDocument();
        expect(screen.getByText(/Machine learning is a subset of AI/)).toBeInTheDocument();
      });

      // Verify all API calls were made with correct conversation history
      expect(apiService.sendMessage).toHaveBeenCalledTimes(3);
      
      // Check that conversation history was maintained
      const thirdCall = (apiService.sendMessage as any).mock.calls[2];
      const messages = thirdCall[0];
      expect(messages).toHaveLength(6); // System + 2 user + 2 assistant + 1 user
    });

    it('should handle error recovery in conversation flow', async () => {
      // Mock API responses: success, error, success
      (apiService.sendMessage as any)
        .mockResolvedValueOnce({
          success: true,
          data: {
            message: 'Hello! How can I help you?',
            id: 'msg-1',
            timestamp: new Date()
          }
        })
        .mockResolvedValueOnce({
          success: false,
          error: {
            code: 'API_ERROR',
            message: 'Service temporarily unavailable'
          }
        })
        .mockResolvedValueOnce({
          success: true,
          data: {
            message: 'I\'m back online! How can I assist you?',
            id: 'msg-3',
            timestamp: new Date()
          }
        });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      // First successful message
      await user.type(textarea, 'Hello');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Hello! How can I help you?')).toBeInTheDocument();
      });

      // Second message that fails
      await user.type(textarea, 'What is AI?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Error.*Service temporarily unavailable/)).toBeInTheDocument();
      });

      // Third message that succeeds
      await user.type(textarea, 'Are you working now?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('I\'m back online! How can I assist you?')).toBeInTheDocument();
      });

      expect(apiService.sendMessage).toHaveBeenCalledTimes(3);
    });
  });

  describe('Feature Integration Tests', () => {
    it('should automatically enable web search for current info queries', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Based on my web search, here are the latest news from today...',
          id: 'msg-web-search',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Was sind die aktuellen Nachrichten heute?');
      await user.click(sendButton);

      await waitFor(() => {
        expect(apiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            useWebSearch: true
          })
        );
      });

      await waitFor(() => {
        expect(screen.getByText(/Based on my web search/)).toBeInTheDocument();
      });
    });

    it('should automatically enable deep thinking for complex queries', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'After careful analysis and deep thinking, I can provide a comprehensive explanation...',
          id: 'msg-deep-thinking',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      const complexQuery = 'Erkläre mir die komplexe Beziehung zwischen Quantencomputing und künstlicher Intelligenz, analysiere die Vor- und Nachteile und bewerte die zukünftigen Entwicklungsmöglichkeiten';
      await user.type(textarea, complexQuery);
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockApiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            enableDeepThinking: true
          })
        );
      });

      await waitFor(() => {
        expect(screen.getByText(/After careful analysis and deep thinking/)).toBeInTheDocument();
      });
    });

    it('should automatically enable tools for calculation queries', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'The calculation result is: 15 × 24 + 100 = 460',
          id: 'msg-calculation',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Berechne 15 * 24 + 100');
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockApiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            useTools: true
          })
        );
      });

      await waitFor(() => {
        expect(screen.getByText(/The calculation result is/)).toBeInTheDocument();
      });
    });

    it('should handle multiple features simultaneously', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'Based on current web data and deep analysis, I calculated that the market trends show...',
          id: 'msg-multi-feature',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      const multiFeatureQuery = 'Analysiere die aktuellen Börsenkurse heute und berechne die prozentuale Veränderung der letzten Woche';
      await user.type(textarea, multiFeatureQuery);
      await user.click(sendButton);

      await waitFor(() => {
        expect(mockApiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            useWebSearch: true,
            enableDeepThinking: true,
            useTools: true
          })
        );
      });
    });
  });

  describe('User Interface Interactions', () => {
    it('should handle suggestion button clicks', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'KI-Technologien umfassen verschiedene Bereiche wie maschinelles Lernen, neuronale Netzwerke...',
          id: 'msg-suggestion',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const suggestionButton = screen.getByText('Erkläre mir KI-Technologien verständlich');
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      // Click suggestion button
      await user.click(suggestionButton);

      // Verify input is populated
      expect(textarea).toHaveValue('Erkläre mir KI-Technologien verständlich');

      // Send the message
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/KI-Technologien umfassen verschiedene Bereiche/)).toBeInTheDocument();
      });
    });

    it('should handle message copying', async () => {
      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: vi.fn().mockResolvedValue(undefined)
        }
      });

      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'This is a test message that can be copied',
          id: 'msg-copy',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('This is a test message that can be copied')).toBeInTheDocument();
      });

      // Find and hover over the message to show action buttons
      const messageCard = screen.getByText('This is a test message that can be copied').closest('.group');
      if (messageCard) {
        await user.hover(messageCard);
        
        const copyButton = screen.getByLabelText('Copy message');
        await user.click(copyButton);
        
        expect(navigator.clipboard.writeText).toHaveBeenCalledWith('This is a test message that can be copied');
      }
    });

    it('should handle chat clearing', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'Test response',
          id: 'msg-clear',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      // Send a message first
      await user.type(textarea, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText('Test response')).toBeInTheDocument();
      });

      // Clear the chat
      const clearButton = screen.getByText('🗑️ Chat löschen');
      await user.click(clearButton);

      // Should return to welcome screen
      expect(screen.queryByText('Test response')).not.toBeInTheDocument();
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
    });
  });

  describe('Security and Rate Limiting', () => {
    it('should handle rate limiting gracefully', async () => {
      // Mock rate limit exceeded
      mockSecurityManager.checkRateLimit.mockReturnValue({
        allowed: false,
        resetTime: Date.now() + 60000
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Test message');
      await user.click(sendButton);

      // Should not call API service
      expect(mockApiService.sendMessage).not.toHaveBeenCalled();
      
      // Input should remain (not cleared)
      expect(textarea).toHaveValue('Test message');
    });

    it('should sanitize malicious input', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'Response to sanitized input',
          id: 'msg-sanitized',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      const maliciousInput = '<script>alert("xss")</script>Hello';
      await user.type(textarea, maliciousInput);
      await user.click(sendButton);

      // Should call sanitizeInput
      expect(mockSecurityManager.sanitizeInput).toHaveBeenCalledWith(maliciousInput);
      
      await waitFor(() => {
        expect(screen.getByText('Response to sanitized input')).toBeInTheDocument();
      });
    });
  });

  describe('Connection and Health Monitoring', () => {
    it('should check connection on component mount', async () => {
      render(<EnhancedSparkChatInterface />);

      await waitFor(() => {
        expect(mockApiService.testConnection).toHaveBeenCalled();
      });
    });

    it('should handle connection failures gracefully', async () => {
      mockApiService.testConnection.mockResolvedValue({
        connected: false,
        error: 'Connection failed'
      });

      render(<EnhancedSparkChatInterface />);

      await waitFor(() => {
        expect(mockApiService.testConnection).toHaveBeenCalled();
      });

      // Component should still render normally
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
    });

    it('should handle API service fallback mode', async () => {
      // Mock fallback response
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: '🔧 **Service Recovery Mode**: The service is temporarily unavailable\n\nI can still help you with basic questions...',
          id: 'msg-fallback',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Test fallback');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/Service Recovery Mode/)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility and Keyboard Navigation', () => {
    it('should support keyboard navigation', async () => {
      mockApiService.sendMessage.mockResolvedValue({
        success: true,
        data: {
          message: 'Keyboard navigation test response',
          id: 'msg-keyboard',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);

      // Focus should be on textarea
      textarea.focus();
      expect(textarea).toHaveFocus();

      // Type message
      await user.type(textarea, 'Test keyboard navigation');

      // Press Enter to send
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockApiService.sendMessage).toHaveBeenCalled();
      });
    });

    it('should have proper ARIA labels and roles', () => {
      render(<EnhancedSparkChatInterface />);

      expect(screen.getByLabelText('Chat message input')).toBeInTheDocument();
      expect(screen.getByLabelText('Send message')).toBeInTheDocument();
      expect(screen.getByLabelText('Attach file')).toBeInTheDocument();
      expect(screen.getByLabelText('Voice input')).toBeInTheDocument();
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Performance and Responsiveness', () => {
    it('should handle rapid message sending', async () => {
      mockApiService.sendMessage.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: {
            message: 'Response',
            id: `msg-${Date.now()}`,
            timestamp: new Date()
          }
        }), 100))
      );

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      // Try to send multiple messages rapidly
      await user.type(textarea, 'First message');
      await user.click(sendButton);

      // Try to send another message immediately
      await user.type(textarea, 'Second message');
      await user.click(sendButton);

      // Should only process one message at a time
      expect(mockApiService.sendMessage).toHaveBeenCalledTimes(1);
    });

    it('should show typing indicator during processing', async () => {
      mockApiService.sendMessage.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: {
            message: 'Delayed response',
            id: 'msg-delayed',
            timestamp: new Date()
          }
        }), 200))
      );

      render(<EnhancedSparkChatInterface />);

      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(textarea, 'Test typing indicator');
      await user.click(sendButton);

      // Should show typing indicator
      expect(screen.getByText('Spark AI is thinking...')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByText('Spark AI is thinking...')).not.toBeInTheDocument();
        expect(screen.getByText('Delayed response')).toBeInTheDocument();
      });
    });
  });
});