import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock environment
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://test.api.com',
  VITE_DEBUG_MODE: 'false',
};

vi.stubGlobal('import.meta', { env: mockEnv });

// Mock dependencies
vi.mock('@/services/apiService', () => ({
  apiService: {
    sendMessage: vi.fn(),
    sendEnhancedGLMRequest: vi.fn(),
    testConnection: vi.fn(),
    getCircuitBreakerStatus: vi.fn(() => ({
      state: 'CLOSED',
      failures: 0,
      healthy: true
    }))
  }
}));

vi.mock('@/config/security', () => ({
  securityManager: {
    sanitizeInput: vi.fn((input) => input),
    checkRateLimit: vi.fn(() => ({ allowed: true })),
    generateSecureRequestId: vi.fn(() => 'test-request-id'),
    logSecurityEvent: vi.fn(),
    validateApiKey: vi.fn(() => true),
    resetRateLimits: vi.fn(),
    setTestConfig: vi.fn()
  }
}));

vi.mock('@/hooks/useNeuralTriggers', () => ({
  useNeuralTriggers: () => ({
    trackMessage: vi.fn(),
    getPersonalizedGreeting: vi.fn(() => 'Hallo!')
  })
}));

vi.mock('@/hooks/useAdaptiveUI', () => ({
  useAdaptiveUI: () => ({
    uiConfig: {
      animationsEnabled: true,
      animationSpeed: 'normal'
    },
    trackTyping: vi.fn(),
    trackInteraction: vi.fn(),
    trackFeatureUsage: vi.fn()
  })
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

import { EnhancedSparkChatInterface } from '../components/spark/EnhancedSparkChatInterface';
import { apiService } from '../services/apiService';

describe('EnhancedSparkChatInterface Unit Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful API connection test
    (apiService.testConnection as any).mockResolvedValue({
      connected: true,
      details: { status: 200, responseTime: 100 }
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the chat interface', () => {
      render(<EnhancedSparkChatInterface />);
      
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/Fragen Sie Spark AI alles/)).toBeInTheDocument();
    });

    it('should render welcome message when no messages exist', () => {
      render(<EnhancedSparkChatInterface />);
      
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
      expect(screen.getByText(/Ihr fortschrittlicher KI-Assistent/)).toBeInTheDocument();
    });

    it('should render suggestion buttons', () => {
      render(<EnhancedSparkChatInterface />);
      
      expect(screen.getByText('Erkläre mir KI-Technologien verständlich')).toBeInTheDocument();
      expect(screen.getByText('Hilf mir bei der Lösung eines Problems')).toBeInTheDocument();
      expect(screen.getByText('Analysiere aktuelle Entwicklungen')).toBeInTheDocument();
      expect(screen.getByText('Schreibe Code für mein Projekt')).toBeInTheDocument();
    });

    it('should render input textarea and send button', () => {
      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      expect(textarea).toBeInTheDocument();
      expect(sendButton).toBeInTheDocument();
    });
  });

  describe('Message Input and Sending', () => {
    it('should update input value when typing', async () => {
      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      
      await user.type(textarea, 'Hello, Spark!');
      
      expect(textarea).toHaveValue('Hello, Spark!');
    });

    it('should send message when send button is clicked', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Hello! How can I help you?',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello, Spark!');
      await user.click(sendButton);
      
      expect(apiService.sendMessage).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            role: 'user',
            content: 'Hello, Spark!'
          })
        ]),
        expect.any(Object)
      );
    });

    it('should send message when Enter key is pressed', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Hello! How can I help you?',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      
      await user.type(textarea, 'Hello, Spark!');
      await user.keyboard('{Enter}');
      
      expect(apiService.sendMessage).toHaveBeenCalled();
    });

    it('should not send message when Shift+Enter is pressed', async () => {
      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      
      await user.type(textarea, 'Hello, Spark!');
      await user.keyboard('{Shift>}{Enter}{/Shift}');
      
      expect(apiService.sendMessage).not.toHaveBeenCalled();
      expect(textarea).toHaveValue('Hello, Spark!\n');
    });

    it('should clear input after sending message', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Hello! How can I help you?',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello, Spark!');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(textarea).toHaveValue('');
      });
    });

    it('should not send empty messages', async () => {
      render(<EnhancedSparkChatInterface />);
      
      const sendButton = screen.getByLabelText('Send message');
      
      await user.click(sendButton);
      
      expect(apiService.sendMessage).not.toHaveBeenCalled();
    });

    it('should not send messages while typing indicator is active', async () => {
      (apiService.sendMessage as any).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: {
            message: 'Response',
            id: 'test-id',
            timestamp: new Date()
          }
        }), 1000))
      );

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'First message');
      await user.click(sendButton);
      
      // Try to send another message while first is processing
      await user.type(textarea, 'Second message');
      await user.click(sendButton);
      
      // Should only be called once
      expect(apiService.sendMessage).toHaveBeenCalledTimes(1);
    });
  });

  describe('Message Display', () => {
    it('should display user and AI messages', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Hello! How can I help you?',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello, Spark!');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('Hello, Spark!')).toBeInTheDocument();
        expect(screen.getByText('Hello! How can I help you?')).toBeInTheDocument();
      });
    });

    it('should show typing indicator while processing', async () => {
      (apiService.sendMessage as any).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: {
            message: 'Response',
            id: 'test-id',
            timestamp: new Date()
          }
        }), 100))
      );

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello');
      await user.click(sendButton);
      
      // Should show thinking indicator
      expect(screen.getByText('Spark AI is thinking...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Spark AI is thinking...')).not.toBeInTheDocument();
      });
    });

    it('should display error messages when API fails', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: false,
        error: {
          code: 'API_ERROR',
          message: 'API request failed'
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText(/Error.*API request failed/)).toBeInTheDocument();
      });
    });
  });

  describe('Feature Toggles', () => {
    it('should enable web search for current info queries', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Here are the latest news...',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Was sind die aktuellen Nachrichten heute?');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(apiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            useWebSearch: true
          })
        );
      });
    });

    it('should enable deep thinking for complex queries', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'After careful analysis...',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Erkläre mir die komplexe Architektur von neuronalen Netzwerken und analysiere deren Vor- und Nachteile');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(apiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            enableDeepThinking: true
          })
        );
      });
    });

    it('should enable tools for calculation queries', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'The calculation result is...',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Berechne 15 * 24 + 100');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(apiService.sendMessage).toHaveBeenCalledWith(
          expect.any(Array),
          expect.objectContaining({
            useTools: true
          })
        );
      });
    });
  });

  describe('Message Actions', () => {
    it('should copy message content when copy button is clicked', async () => {
      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: vi.fn().mockResolvedValue(undefined)
        }
      });

      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'This is a test response',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('This is a test response')).toBeInTheDocument();
      });

      // Hover over message to show action buttons
      const messageCard = screen.getByText('This is a test response').closest('.group');
      if (messageCard) {
        await user.hover(messageCard);
        
        const copyButton = screen.getByLabelText('Copy message');
        await user.click(copyButton);
        
        expect(navigator.clipboard.writeText).toHaveBeenCalledWith('This is a test response');
      }
    });

    it('should regenerate response when regenerate button is clicked', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'This is a test response',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('This is a test response')).toBeInTheDocument();
      });

      // Hover over message to show action buttons
      const messageCard = screen.getByText('This is a test response').closest('.group');
      if (messageCard) {
        await user.hover(messageCard);
        
        const regenerateButton = screen.getByLabelText('Regenerate response');
        await user.click(regenerateButton);
        
        // Should show regenerating message
        await waitFor(() => {
          expect(screen.getByText(/Here's a regenerated response/)).toBeInTheDocument();
        });
      }
    });
  });

  describe('Suggestion Buttons', () => {
    it('should populate input when suggestion button is clicked', async () => {
      render(<EnhancedSparkChatInterface />);
      
      const suggestionButton = screen.getByText('Erkläre mir KI-Technologien verständlich');
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      
      await user.click(suggestionButton);
      
      expect(textarea).toHaveValue('Erkläre mir KI-Technologien verständlich');
    });
  });

  describe('Chat Management', () => {
    it('should clear all messages when clear chat button is clicked', async () => {
      (apiService.sendMessage as any).mockResolvedValue({
        success: true,
        data: {
          message: 'Test response',
          id: 'test-id-123',
          timestamp: new Date()
        }
      });

      render(<EnhancedSparkChatInterface />);
      
      // Send a message first
      const textarea = screen.getByPlaceholderText(/Fragen Sie Spark AI alles/);
      const sendButton = screen.getByLabelText('Send message');
      
      await user.type(textarea, 'Hello');
      await user.click(sendButton);
      
      await waitFor(() => {
        expect(screen.getByText('Test response')).toBeInTheDocument();
      });

      // Clear chat
      const clearButton = screen.getByText('🗑️ Chat löschen');
      await user.click(clearButton);
      
      // Messages should be cleared
      expect(screen.queryByText('Test response')).not.toBeInTheDocument();
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
    });
  });

  describe('Connection Status', () => {
    it('should check API connection on mount', async () => {
      render(<EnhancedSparkChatInterface />);
      
      await waitFor(() => {
        expect(apiService.testConnection).toHaveBeenCalled();
      });
    });

    it('should handle connection failures gracefully', async () => {
      (apiService.testConnection as any).mockResolvedValue({
        connected: false,
        error: 'Connection failed'
      });

      render(<EnhancedSparkChatInterface />);
      
      await waitFor(() => {
        expect(apiService.testConnection).toHaveBeenCalled();
      });

      // Component should still render normally
      expect(screen.getByText('Willkommen bei Spark AI')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<EnhancedSparkChatInterface />);
      
      expect(screen.getByLabelText('Chat message input')).toBeInTheDocument();
      expect(screen.getByLabelText('Send message')).toBeInTheDocument();
      expect(screen.getByLabelText('Attach file')).toBeInTheDocument();
      expect(screen.getByLabelText('Voice input')).toBeInTheDocument();
    });

    it('should have proper role attributes', () => {
      render(<EnhancedSparkChatInterface />);
      
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });
});