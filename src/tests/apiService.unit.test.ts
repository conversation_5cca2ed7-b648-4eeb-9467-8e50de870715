import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock environment for testing
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://open.bigmodel.cn/api/paas/v4',
  VITE_DEBUG_MODE: 'false',
  VITE_DEFAULT_MODEL: 'glm-4-plus',
  VITE_MAX_TOKENS: '4000',
  VITE_TEMPERATURE: '0.7',
};

vi.stubGlobal('import.meta', { env: mockEnv });

import { apiService, type ChatMessage, type ApiResponse } from '../services/apiService';
import { apiConfig } from '../config/api';

// Mock fetch globally
global.fetch = vi.fn();

describe('ApiService Unit Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Configure API for testing
    apiConfig.updateConfig({
      apiKey: 'test-************************************.1234567890123456',
      endpoint: 'https://open.bigmodel.cn/api/paas/v4',
      debugMode: false,
      defaultModel: 'glm-4-plus',
      maxTokens: 4000,
      temperature: 0.7
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('sendMessage', () => {
    it('should send a basic message successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Hello! How can I help you today?'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const result = await apiService.sendMessage(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Hello! How can I help you today?');
      expect(result.data?.id).toBe('test-id-123');
      expect(result.data?.timestamp).toBeInstanceOf(Date);
    });

    it('should handle API errors gracefully', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid request format'),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response instead of error
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should handle network errors with fallback', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      const result = await apiService.sendMessage(messages);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });

    it('should sanitize input messages', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Response to sanitized input'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: '<script>alert("xss")</script>Hello' }
      ];

      const result = await apiService.sendMessage(messages);

      expect(result.success).toBe(true);
      // Verify that fetch was called (meaning input was sanitized and allowed)
      expect(global.fetch).toHaveBeenCalled();
    });

    it('should respect rate limiting', async () => {
      const clientId = 'test-client-rate-limit';
      
      // Mock successful responses
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      // Make multiple requests to trigger rate limiting
      const results = [];
      for (let i = 0; i < 12; i++) {
        const result = await apiService.sendMessage(messages, { clientId });
        results.push(result);
      }

      // Some requests should be rate limited
      const rateLimitedResults = results.filter(r => 
        !r.success && r.error?.code === 'RATE_LIMIT_EXCEEDED'
      );
      
      expect(rateLimitedResults.length).toBeGreaterThan(0);
    });

    it('should enable web search when requested', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Here are the latest search results...'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'What are the latest news today?' }
      ];

      const result = await apiService.sendMessage(messages, {
        useWebSearch: true
      });

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('web_search')
        })
      );
    });

    it('should enable deep thinking when requested', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'After careful analysis...'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Analyze this complex problem...' }
      ];

      const result = await apiService.sendMessage(messages, {
        enableDeepThinking: true
      });

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('thinking')
        })
      );
    });
  });

  describe('Circuit Breaker', () => {
    it('should track failures and open circuit breaker', async () => {
      // Mock consecutive failures
      (global.fetch as any).mockRejectedValue(new Error('Server error'));

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      // Make multiple failed requests
      for (let i = 0; i < 6; i++) {
        await apiService.sendMessage(messages);
      }

      const status = apiService.getCircuitBreakerStatus();
      expect(status.failures).toBeGreaterThan(0);
      expect(status.healthy).toBe(false);
    });

    it('should provide circuit breaker status', () => {
      const status = apiService.getCircuitBreakerStatus();
      
      expect(status).toHaveProperty('state');
      expect(status).toHaveProperty('failures');
      expect(status).toHaveProperty('healthy');
      expect(['CLOSED', 'OPEN', 'HALF_OPEN']).toContain(status.state);
      expect(typeof status.failures).toBe('number');
      expect(typeof status.healthy).toBe('boolean');
    });

    it('should reset circuit breaker on successful requests', async () => {
      // First, cause some failures
      (global.fetch as any).mockRejectedValue(new Error('Server error'));
      
      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test message' }
      ];

      await apiService.sendMessage(messages);
      
      let status = apiService.getCircuitBreakerStatus();
      const initialFailures = status.failures;

      // Then mock a successful response
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Success response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      await apiService.sendMessage(messages);
      
      status = apiService.getCircuitBreakerStatus();
      // Failures should be reduced after successful request
      expect(status.failures).toBeLessThanOrEqual(initialFailures);
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'ping'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await apiService.testConnection();

      expect(result.connected).toBe(true);
      expect(result.details?.status).toBe(200);
      expect(result.details?.responseTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle connection failures', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Connection failed'));

      const result = await apiService.testConnection();

      expect(result.connected).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.details?.responseTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('healthCheck', () => {
    it('should perform comprehensive health check', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Health check response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await apiService.healthCheck();

      expect(result).toHaveProperty('healthy');
      expect(result).toHaveProperty('checks');
      expect(result.checks).toHaveProperty('connection');
      expect(result.checks).toHaveProperty('authentication');
      expect(result.checks).toHaveProperty('rateLimit');
      expect(result.checks).toHaveProperty('configuration');
      expect(typeof result.healthy).toBe('boolean');
    });
  });

  describe('Enhanced GLM Request', () => {
    it('should send enhanced GLM request with all features', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'test-id-123',
          choices: [{
            message: {
              content: 'Enhanced response with thinking and tools'
            }
          }],
          usage: {
            prompt_tokens: 50,
            completion_tokens: 100,
            total_tokens: 150
          }
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const request = {
        messages: [
          { role: 'user' as const, content: 'Complex question requiring analysis' }
        ],
        thinking: {
          type: 'enabled' as const,
          budget_tokens: 2000
        },
        tools: [
          {
            type: 'web_search',
            web_search: {
              enable: true
            }
          }
        ],
        tool_choice: 'auto' as const,
        temperature: 0.7,
        max_tokens: 4000
      };

      const result = await apiService.sendEnhancedGLMRequest(request);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Enhanced response with thinking and tools');
      expect(result.data?.usage).toBeDefined();
      expect(result.data?.usage?.total_tokens).toBe(150);
    });

    it('should handle enhanced GLM request errors', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid enhanced request'),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const request = {
        messages: [
          { role: 'user' as const, content: 'Test message' }
        ]
      };

      const result = await apiService.sendEnhancedGLMRequest(request);

      // Should return fallback response
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Fallback Mode Active');
    });
  });

  describe('Mock Response Generation', () => {
    it('should generate appropriate mock responses', async () => {
      const messages: ChatMessage[] = [
        { role: 'user', content: 'What is artificial intelligence?' }
      ];

      const result = await apiService.getMockResponse(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBeDefined();
      expect(result.data?.message.length).toBeGreaterThan(0);
      expect(result.data?.id).toBeDefined();
      expect(result.data?.timestamp).toBeInstanceOf(Date);
    });

    it('should generate contextual mock responses', async () => {
      const messages: ChatMessage[] = [
        { role: 'user', content: 'Write code for a React component' }
      ];

      const result = await apiService.getMockResponse(messages);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('```');
    });
  });

  describe('Error Categorization', () => {
    it('should categorize different error types correctly', async () => {
      const testCases = [
        { error: new Error('fetch failed'), expectedType: 'NETWORK_ERROR' },
        { error: new Error('timeout'), expectedType: 'TIMEOUT_ERROR' },
        { error: new Error('401 Unauthorized'), expectedType: 'AUTH_ERROR' },
        { error: new Error('429 Too Many Requests'), expectedType: 'RATE_LIMIT_ERROR' },
        { error: new Error('500 Internal Server Error'), expectedType: 'SERVER_ERROR' }
      ];

      for (const testCase of testCases) {
        (global.fetch as any).mockRejectedValue(testCase.error);

        const messages: ChatMessage[] = [
          { role: 'user', content: 'Test message' }
        ];

        const result = await apiService.sendMessage(messages);

        // Most errors should trigger fallback mode
        if (result.success) {
          expect(result.data?.message).toContain('Fallback Mode Active');
        } else {
          expect(result.error?.code).toBe(testCase.expectedType);
        }
      }
    });
  });
});