import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock environment for testing
const mockEnv = {
  VITE_API_KEY: 'test-************************************.1234567890123456',
  VITE_API_ENDPOINT: 'https://open.bigmodel.cn/api/paas/v4',
  VITE_DEBUG_MODE: 'false',
};

vi.stubGlobal('import.meta', { env: mockEnv });

import { apiService, type ChatMessage } from '../services/apiService';
import { apiConfig } from '../config/api';

// Mock fetch for performance testing
global.fetch = vi.fn();

describe('API Performance Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Configure API for testing
    apiConfig.updateConfig({
      apiKey: 'test-************************************.1234567890123456',
      endpoint: 'https://open.bigmodel.cn/api/paas/v4',
      debugMode: false,
      defaultModel: 'glm-4-plus',
      maxTokens: 4000,
      temperature: 0.7
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Response Time Performance', () => {
    it('should complete basic requests within acceptable time limits', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'perf-test-1',
          choices: [{
            message: {
              content: 'Quick response for performance testing'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Simple test message' }
      ];

      const startTime = performance.now();
      const result = await apiService.sendMessage(messages);
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent requests efficiently', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'concurrent-test',
          choices: [{
            message: {
              content: 'Concurrent response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Concurrent test message' }
      ];

      const concurrentRequests = 5;
      const startTime = performance.now();

      // Send multiple concurrent requests
      const promises = Array.from({ length: concurrentRequests }, () =>
        apiService.sendMessage(messages, { clientId: `client-${Math.random()}` })
      );

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // All requests should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Concurrent requests should not take significantly longer than sequential
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should measure and track response times accurately', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'timing-test',
          choices: [{
            message: {
              content: 'Response time measurement test'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      // Add artificial delay to test timing measurement
      (global.fetch as any).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve(mockResponse), 100)
        )
      );

      const connectionResult = await apiService.testConnection();

      expect(connectionResult.connected).toBe(true);
      expect(connectionResult.details?.responseTime).toBeGreaterThanOrEqual(100);
      expect(connectionResult.details?.responseTime).toBeLessThan(1000);
    });
  });

  describe('Load Testing', () => {
    it('should handle high-frequency requests without degradation', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'load-test',
          choices: [{
            message: {
              content: 'Load test response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Load test message' }
      ];

      const requestCount = 20;
      const responseTimes: number[] = [];

      // Send requests sequentially to measure individual response times
      for (let i = 0; i < requestCount; i++) {
        const startTime = performance.now();
        const result = await apiService.sendMessage(messages, { 
          clientId: `load-test-client-${i}` 
        });
        const endTime = performance.now();
        
        expect(result.success).toBe(true);
        responseTimes.push(endTime - startTime);
      }

      // Calculate performance metrics
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);

      // Performance assertions
      expect(averageResponseTime).toBeLessThan(1000); // Average under 1 second
      expect(maxResponseTime).toBeLessThan(2000); // Max under 2 seconds
      expect(minResponseTime).toBeGreaterThan(0); // Minimum should be positive

      // Consistency check - max shouldn't be more than 3x average
      expect(maxResponseTime).toBeLessThan(averageResponseTime * 3);
    });

    it('should maintain performance under memory pressure', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'memory-test',
          choices: [{
            message: {
              content: 'A'.repeat(10000) // Large response to test memory handling
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const largeMessages: ChatMessage[] = [
        { role: 'user', content: 'B'.repeat(5000) } // Large input message
      ];

      const memoryTestCount = 10;
      const responseTimes: number[] = [];

      for (let i = 0; i < memoryTestCount; i++) {
        const startTime = performance.now();
        const result = await apiService.sendMessage(largeMessages, {
          clientId: `memory-test-${i}`
        });
        const endTime = performance.now();

        expect(result.success).toBe(true);
        expect(result.data?.message).toHaveLength(10000);
        responseTimes.push(endTime - startTime);
      }

      // Performance should remain consistent even with large payloads
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      expect(averageResponseTime).toBeLessThan(2000); // Should handle large payloads efficiently
    });
  });

  describe('Circuit Breaker Performance', () => {
    it('should fail fast when circuit breaker is open', async () => {
      // Cause circuit breaker to open by triggering failures
      (global.fetch as any).mockRejectedValue(new Error('Service unavailable'));

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test circuit breaker' }
      ];

      // Trigger enough failures to open circuit breaker
      for (let i = 0; i < 6; i++) {
        await apiService.sendMessage(messages, { clientId: `circuit-test-${i}` });
      }

      // Now test that subsequent requests fail fast
      const startTime = performance.now();
      const result = await apiService.sendMessage(messages, { clientId: 'circuit-test-fast-fail' });
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      // Should return fallback response quickly
      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('Service Recovery Mode');
      expect(responseTime).toBeLessThan(100); // Should fail fast (under 100ms)
    });

    it('should track circuit breaker performance metrics', () => {
      const status = apiService.getCircuitBreakerStatus();
      
      expect(status).toHaveProperty('state');
      expect(status).toHaveProperty('failures');
      expect(status).toHaveProperty('healthy');
      expect(typeof status.failures).toBe('number');
      expect(typeof status.healthy).toBe('boolean');
      expect(['CLOSED', 'OPEN', 'HALF_OPEN']).toContain(status.state);
    });
  });

  describe('Retry Mechanism Performance', () => {
    it('should implement exponential backoff efficiently', async () => {
      let callCount = 0;
      const callTimes: number[] = [];

      (global.fetch as any).mockImplementation(() => {
        callTimes.push(performance.now());
        callCount++;
        
        if (callCount < 3) {
          return Promise.reject(new Error('Temporary failure'));
        }
        
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({
            id: 'retry-test',
            choices: [{
              message: {
                content: 'Success after retries'
              }
            }]
          }),
          headers: new Map([['content-type', 'application/json']])
        });
      });

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test retry mechanism' }
      ];

      const startTime = performance.now();
      const result = await apiService.sendMessage(messages);
      const endTime = performance.now();

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Success after retries');
      expect(callCount).toBe(3);

      // Verify exponential backoff timing
      if (callTimes.length >= 2) {
        const firstRetryDelay = callTimes[1] - callTimes[0];
        const secondRetryDelay = callTimes[2] - callTimes[1];
        
        // Second delay should be longer than first (exponential backoff)
        expect(secondRetryDelay).toBeGreaterThan(firstRetryDelay);
      }

      // Total time should be reasonable despite retries
      const totalTime = endTime - startTime;
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });

  describe('Streaming Performance', () => {
    it('should handle streaming responses efficiently', async () => {
      const streamChunks = [
        'data: {"choices":[{"delta":{"content":"Hello"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" there!"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" How"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" can"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" I"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":" help?"}}]}\n\n',
        'data: [DONE]\n\n'
      ];

      let chunkIndex = 0;
      const mockResponse = {
        ok: true,
        status: 200,
        body: {
          getReader: () => ({
            read: vi.fn().mockImplementation(() => {
              if (chunkIndex < streamChunks.length) {
                const chunk = streamChunks[chunkIndex++];
                return Promise.resolve({
                  done: false,
                  value: new TextEncoder().encode(chunk)
                });
              } else {
                return Promise.resolve({
                  done: true,
                  value: undefined
                });
              }
            })
          })
        },
        headers: new Map([['content-type', 'text/event-stream']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test streaming performance' }
      ];

      let tokenCount = 0;
      const tokenTimes: number[] = [];
      const startTime = performance.now();

      const streamCallback = {
        onToken: (token: string) => {
          tokenCount++;
          tokenTimes.push(performance.now());
        },
        onComplete: (message: string) => {
          expect(message).toBe('Hello there! How can I help?');
        },
        onError: (error: Error) => {
          throw error;
        }
      };

      const result = await apiService.sendMessage(messages, {
        stream: true,
        onStream: streamCallback
      });

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(tokenCount).toBe(6); // Should receive all tokens
      expect(totalTime).toBeLessThan(5000); // Should complete streaming within 5 seconds

      // Verify consistent token delivery timing
      if (tokenTimes.length > 1) {
        const tokenIntervals = tokenTimes.slice(1).map((time, index) => 
          time - tokenTimes[index]
        );
        const averageInterval = tokenIntervals.reduce((a, b) => a + b, 0) / tokenIntervals.length;
        
        // Token intervals should be reasonably consistent
        expect(averageInterval).toBeLessThan(1000); // Average interval under 1 second
      }
    });
  });

  describe('Health Check Performance', () => {
    it('should perform health checks quickly', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'health-check',
          choices: [{
            message: {
              content: 'Health check response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const startTime = performance.now();
      const healthResult = await apiService.healthCheck();
      const endTime = performance.now();
      const healthCheckTime = endTime - startTime;

      expect(healthResult.healthy).toBe(true);
      expect(healthCheckTime).toBeLessThan(3000); // Health check should complete within 3 seconds

      // Verify all health checks are performed
      expect(healthResult.checks.connection.status).toBe('healthy');
      expect(healthResult.checks.authentication.status).toBe('healthy');
      expect(healthResult.checks.rateLimit.status).toBe('healthy');
      expect(healthResult.checks.configuration.status).toBe('healthy');
    });

    it('should handle health check failures quickly', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Health check failed'));

      const startTime = performance.now();
      const healthResult = await apiService.healthCheck();
      const endTime = performance.now();
      const healthCheckTime = endTime - startTime;

      expect(healthResult.healthy).toBe(false);
      expect(healthCheckTime).toBeLessThan(5000); // Should fail quickly within 5 seconds
    });
  });

  describe('Memory and Resource Management', () => {
    it('should not leak memory during repeated operations', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'memory-leak-test',
          choices: [{
            message: {
              content: 'Memory test response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Memory leak test' }
      ];

      // Simulate repeated operations
      const operationCount = 50;
      const results = [];

      for (let i = 0; i < operationCount; i++) {
        const result = await apiService.sendMessage(messages, {
          clientId: `memory-test-${i}`
        });
        results.push(result);
        
        // Verify each operation succeeds
        expect(result.success).toBe(true);
      }

      // All operations should complete successfully
      expect(results).toHaveLength(operationCount);
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    it('should handle large conversation histories efficiently', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'large-history-test',
          choices: [{
            message: {
              content: 'Response to large conversation'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      // Create a large conversation history
      const largeConversation: ChatMessage[] = [];
      for (let i = 0; i < 100; i++) {
        largeConversation.push(
          { role: 'user', content: `User message ${i}` },
          { role: 'assistant', content: `Assistant response ${i}` }
        );
      }
      largeConversation.push({ role: 'user', content: 'Final question' });

      const startTime = performance.now();
      const result = await apiService.sendMessage(largeConversation);
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(5000); // Should handle large history within 5 seconds
    });
  });

  describe('Performance Monitoring and Metrics', () => {
    it('should provide performance metrics for monitoring', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          id: 'metrics-test',
          choices: [{
            message: {
              content: 'Metrics test response'
            }
          }]
        }),
        headers: new Map([['content-type', 'application/json']])
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      // Test connection to get performance metrics
      const connectionResult = await apiService.testConnection();

      expect(connectionResult.connected).toBe(true);
      expect(connectionResult.details).toBeDefined();
      expect(connectionResult.details?.responseTime).toBeGreaterThanOrEqual(0);
      expect(connectionResult.details?.status).toBe(200);
      expect(typeof connectionResult.details?.responseTime).toBe('number');
    });

    it('should track circuit breaker performance impact', async () => {
      // Get initial circuit breaker status
      const initialStatus = apiService.getCircuitBreakerStatus();
      expect(initialStatus.healthy).toBe(true);

      // Cause some failures to test performance impact
      (global.fetch as any).mockRejectedValue(new Error('Performance test failure'));

      const messages: ChatMessage[] = [
        { role: 'user', content: 'Circuit breaker performance test' }
      ];

      const startTime = performance.now();
      await apiService.sendMessage(messages);
      const endTime = performance.now();
      const failureResponseTime = endTime - startTime;

      const statusAfterFailure = apiService.getCircuitBreakerStatus();
      expect(statusAfterFailure.failures).toBeGreaterThan(initialStatus.failures);

      // Failure handling should still be fast due to fallback
      expect(failureResponseTime).toBeLessThan(2000);
    });
  });
});