/**
 * Environment Variables Management Tests
 * Tests for environment configuration, validation, and health checks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('Environment Manager', () => {
  describe('Configuration Loading', () => {
    it('should validate environment variables correctly', () => {
      // Test basic validation logic
      const testCases = [
        { key: 'VITE_API_ENDPOINT', value: 'https://api.example.com', valid: true },
        { key: 'VITE_API_ENDPOINT', value: 'invalid-url', valid: false },
        { key: 'VITE_MAX_TOKENS', value: '4000', valid: true },
        { key: 'VITE_MAX_TOKENS', value: 'not-a-number', valid: false },
        { key: 'VITE_TEMPERATURE', value: '0.7', valid: true },
        { key: 'VITE_TEMPERATURE', value: '3.0', valid: false }, // > 2.0
      ];

      testCases.forEach(({ key, value, valid }) => {
        // Basic validation test
        if (key === 'VITE_API_ENDPOINT') {
          try {
            new URL(value);
            expect(valid).toBe(true);
          } catch {
            expect(valid).toBe(false);
          }
        }
        
        if (key === 'VITE_MAX_TOKENS') {
          const parsed = parseFloat(value);
          const isValid = !isNaN(parsed) && parsed >= 1 && parsed <= 32000;
          expect(isValid).toBe(valid);
        }
        
        if (key === 'VITE_TEMPERATURE') {
          const parsed = parseFloat(value);
          const isValid = !isNaN(parsed) && parsed >= 0 && parsed <= 2;
          expect(isValid).toBe(valid);
        }
      });
    });

    it('should handle missing environment variables gracefully', () => {
      // Test that the system can handle missing variables
      const mockGetEnvVar = (key: string, defaultValue: string = '') => {
        const mockValues: Record<string, string> = {
          'VITE_API_ENDPOINT': 'https://open.bigmodel.cn/api/paas/v4',
          'VITE_API_VERSION': 'v4',
          'VITE_DEFAULT_MODEL': 'glm-4-flash',
        };
        
        return mockValues[key] || defaultValue;
      };

      // Test with some values present
      expect(mockGetEnvVar('VITE_API_ENDPOINT')).toBe('https://open.bigmodel.cn/api/paas/v4');
      expect(mockGetEnvVar('VITE_MISSING_VAR', 'default')).toBe('default');
      expect(mockGetEnvVar('VITE_MISSING_VAR')).toBe('');
    });

    it('should validate API key format', () => {
      const validApiKey = 'abcd1234efgh5678ijkl9012mnop3456.qrst7890uvwx1234';
      const invalidApiKey1 = 'too-short';
      const invalidApiKey2 = 'abcd1234efgh5678ijkl9012mnop3456'; // missing dot and second part
      
      const apiKeyPattern = /^[a-zA-Z0-9]{32}\.[a-zA-Z0-9]{16}$/;
      
      expect(apiKeyPattern.test(validApiKey)).toBe(true);
      expect(apiKeyPattern.test(invalidApiKey1)).toBe(false);
      expect(apiKeyPattern.test(invalidApiKey2)).toBe(false);
    });
  });

  describe('Environment Detection', () => {
    it('should detect different environments correctly', () => {
      const testEnvironmentDetection = (nodeEnv: string, vercelEnv: string, expected: string) => {
        // Mock environment detection logic
        if (vercelEnv === 'production') return 'production';
        if (vercelEnv === 'preview') return 'staging';
        if (nodeEnv === 'production') return 'production';
        if (nodeEnv === 'test') return 'staging';
        return 'development';
      };

      expect(testEnvironmentDetection('development', '')).toBe('development');
      expect(testEnvironmentDetection('production', '')).toBe('production');
      expect(testEnvironmentDetection('test', '')).toBe('staging');
      expect(testEnvironmentDetection('development', 'production')).toBe('production');
      expect(testEnvironmentDetection('development', 'preview')).toBe('staging');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate CORS origins correctly', () => {
      const testCorsValidation = (origin: string, allowedOrigins: string[]): boolean => {
        let hostname: string;
        try {
          const url = new URL(origin);
          hostname = url.hostname;
        } catch {
          hostname = origin;
        }
        
        return allowedOrigins.some(allowedOrigin => {
          if (allowedOrigin.includes('*')) {
            const pattern = allowedOrigin.replace(/\*/g, '.*');
            const regex = new RegExp(`^${pattern}$`);
            return regex.test(hostname);
          }
          return hostname === allowedOrigin || hostname.endsWith(`.${allowedOrigin}`);
        });
      };

      const allowedOrigins = ['localhost', '127.0.0.1', '*.vercel.app'];
      
      expect(testCorsValidation('http://localhost:3000', allowedOrigins)).toBe(true);
      expect(testCorsValidation('https://myapp.vercel.app', allowedOrigins)).toBe(true);
      expect(testCorsValidation('https://evil.com', allowedOrigins)).toBe(false);
    });
  });
});