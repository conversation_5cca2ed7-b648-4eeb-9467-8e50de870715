import { useState } from "react";
import { SparkHeader } from "@/components/spark/SparkHeader";
import { EnhancedSparkChatInterface } from "@/components/spark/EnhancedSparkChatInterface";
import { SparkSidebar } from "@/components/spark/SparkSidebar";

const Index = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string>();

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSelectConversation = (id: string) => {
    setCurrentConversationId(id);
    setSidebarOpen(false); // Close sidebar on mobile after selection
  };

  const handleNewConversation = () => {
    setCurrentConversationId(undefined);
    setSidebarOpen(false);
  };

  const handleDeleteConversation = (id: string) => {
    if (currentConversationId === id) {
      setCurrentConversationId(undefined);
    }
    // Here you would typically delete from your state/database
  };

  return (
    <div className="min-h-screen bg-background flex flex-col relative overflow-hidden">
      <SparkHeader 
        onToggleSidebar={handleToggleSidebar}
        showSidebarToggle={true}
      />
      
      <SparkSidebar
        isOpen={sidebarOpen}
        onToggle={handleToggleSidebar}
        currentConversationId={currentConversationId}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
        onDeleteConversation={handleDeleteConversation}
      />
      
      <main className="flex-1 flex">
        <EnhancedSparkChatInterface />
      </main>
    </div>
  );
};

export default Index;
