/**
 * Vercel Postgres Integration
 * Direct integration with Vercel's native Postgres database
 */

import { sql } from '@vercel/postgres';
import { environmentManager } from '../config/environment';

export interface User {
  id: string;
  email: string | null;
  name: string | null;
  avatar: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface Conversation {
  id: string;
  user_id: string | null;
  title: string | null;
  is_archived: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Message {
  id: string;
  conversation_id: string;
  user_id: string | null;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: any;
  created_at: Date;
}

export interface ApiUsage {
  id: string;
  user_id: string | null;
  model: string;
  tokens: number;
  cost: number | null;
  endpoint: string;
  status: string;
  error_code: string | null;
  error_message: string | null;
  created_at: Date;
}

export class VercelPostgresService {
  private static instance: VercelPostgresService;

  private constructor() {}

  public static getInstance(): VercelPostgresService {
    if (!VercelPostgresService.instance) {
      VercelPostgresService.instance = new VercelPostgresService();
    }
    return VercelPostgresService.instance;
  }

  /**
   * Initialize database schema
   */
  public async initializeSchema(): Promise<void> {
    try {
      console.log('🔄 Initializing Vercel Postgres schema...');

      // Create users table
      await sql`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE,
          name VARCHAR(255),
          avatar TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      console.log('✅ Users table created');
    } catch (error) {
      console.error('❌ Schema initialization failed:', error);
      throw error;
    }
  }
}