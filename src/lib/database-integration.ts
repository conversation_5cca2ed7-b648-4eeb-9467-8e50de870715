/**
 * Vercel Postgres Integration
 * Optimized for Vercel's native storage solutions
 */

import { sql } from "@vercel/postgres";
import { environmentManager } from "../config/environment";

export interface DatabaseHealth {
  connected: boolean;
  latency?: number;
  region?: string;
  error?: string;
  timestamp: Date;
}

export interface User {
  id: string;
  email?: string;
  name?: string;
  avatar?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Conversation {
  id: string;
  user_id?: string;
  title?: string;
  is_archived: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Message {
  id: string;
  conversation_id: string;
  user_id?: string;
  role: "user" | "assistant" | "system";
  content: string;
  metadata?: Record<string, any>;
  created_at: Date;
}

export interface ApiUsage {
  id: string;
  user_id?: string;
  model: string;
  tokens: number;
  cost?: number;
  endpoint: string;
  status: "success" | "error";
  error_code?: string;
  error_message?: string;
  created_at: Date;
}

export class VercelDatabaseService {
  private static instance: VercelDatabaseService;

  private constructor() {}

  public static getInstance(): VercelDatabaseService {
    if (!VercelDatabaseService.instance) {
      VercelDatabaseService.instance = new VercelDatabaseService();
    }
    return VercelDatabaseService.instance;
  }

  /**
   * Initialize database schema
   * Creates all necessary tables with proper indexes for performance
   */
  public async initializeSchema(): Promise<void> {
    try {
      console.log("🔄 Initializing Vercel Postgres schema...");

      // Create users table
      await sql`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE,
          name VARCHAR(255),
          avatar TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create conversations table
      await sql`
        CREATE TABLE IF NOT EXISTS conversations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          title VARCHAR(500),
          is_archived BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create messages table
      await sql`
        CREATE TABLE IF NOT EXISTS messages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
          user_id UUID REFERENCES users(id) ON DELETE SET NULL,
          role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
          content TEXT NOT NULL,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create api_usage table for monitoring
      await sql`
        CREATE TABLE IF NOT EXISTS api_usage (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE SET NULL,
          model VARCHAR(100) NOT NULL,
          tokens INTEGER NOT NULL,
          cost DECIMAL(10,6),
          endpoint VARCHAR(255) NOT NULL,
          status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
          error_code VARCHAR(50),
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create system_config table
      await sql`
        CREATE TABLE IF NOT EXISTS system_config (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          key VARCHAR(255) UNIQUE NOT NULL,
          value TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create performance indexes
      await sql`
        CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
        CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
        CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at ASC);
        CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage(user_id);
        CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);
      `;

      // Create updated_at trigger function
      await sql`
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $$ language 'plpgsql';
      `;

      // Create triggers for updated_at
      await sql`
        DROP TRIGGER IF EXISTS update_users_updated_at ON users;
        CREATE TRIGGER update_users_updated_at 
          BEFORE UPDATE ON users 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      `;

      await sql`
        DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
        CREATE TRIGGER update_conversations_updated_at 
          BEFORE UPDATE ON conversations 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      `;

      await sql`
        DROP TRIGGER IF EXISTS update_system_config_updated_at ON system_config;
        CREATE TRIGGER update_system_config_updated_at 
          BEFORE UPDATE ON system_config 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      `;

      console.log("✅ Database schema initialized successfully");

      // Seed initial system configuration
      await this.seedSystemConfig();
    } catch (error) {
      console.error("❌ Error initializing database schema:", error);
      throw error;
    }
  }

  /**
   * Seed initial system configuration
   */
  private async seedSystemConfig(): Promise<void> {
    const configs = [
      { key: "app_version", value: "1.0.0" },
      { key: "maintenance_mode", value: "false" },
      { key: "max_conversations_per_user", value: "100" },
      { key: "max_messages_per_conversation", value: "1000" },
      { key: "api_rate_limit", value: "60" },
      { key: "max_tokens_per_request", value: "4000" },
    ];

    for (const config of configs) {
      await sql`
        INSERT INTO system_config (key, value)
        VALUES (${config.key}, ${config.value})
        ON CONFLICT (key) DO NOTHING;
      `;
    }

    console.log("✅ System configuration seeded");
  }

  /**
   * Health check with latency measurement
   */
  public async healthCheck(): Promise<DatabaseHealth> {
    const startTime = Date.now();

    try {
      const result = await sql`
        SELECT 
          current_database() as database,
          current_setting('server_version') as version,
          current_setting('timezone') as timezone;
      `;

      const latency = Date.now() - startTime;

      return {
        connected: true,
        latency,
        region: process.env.VERCEL_REGION || "unknown",
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        connected: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date(),
      };
    }
  }

  // User Operations
  public async createUser(data: {
    email?: string;
    name?: string;
    avatar?: string;
  }): Promise<User> {
    const result = await sql`
      INSERT INTO users (email, name, avatar)
      VALUES (${data.email || null}, ${data.name || null}, ${
      data.avatar || null
    })
      RETURNING *;
    `;
    return result.rows[0] as User;
  }

  public async getUserById(id: string): Promise<User | null> {
    const result = await sql`
      SELECT * FROM users WHERE id = ${id};
    `;
    return (result.rows[0] as User) || null;
  }

  public async getUserByEmail(email: string): Promise<User | null> {
    const result = await sql`
      SELECT * FROM users WHERE email = ${email};
    `;
    return (result.rows[0] as User) || null;
  }

  public async updateUser(
    id: string,
    data: {
      email?: string;
      name?: string;
      avatar?: string;
    }
  ): Promise<User> {
    const result = await sql`
      UPDATE users 
      SET 
        email = COALESCE(${data.email}, email),
        name = COALESCE(${data.name}, name),
        avatar = COALESCE(${data.avatar}, avatar)
      WHERE id = ${id}
      RETURNING *;
    `;
    return result.rows[0] as User;
  }

  // Conversation Operations
  public async createConversation(data: {
    userId?: string;
    title?: string;
  }): Promise<Conversation> {
    const result = await sql`
      INSERT INTO conversations (user_id, title)
      VALUES (${data.userId || null}, ${data.title || null})
      RETURNING *;
    `;
    return result.rows[0] as Conversation;
  }

  public async getConversation(id: string): Promise<{
    conversation: Conversation;
    messages: Message[];
  } | null> {
    const conversationResult = await sql`
      SELECT * FROM conversations WHERE id = ${id} AND is_archived = FALSE;
    `;

    if (conversationResult.rows.length === 0) {
      return null;
    }

    const messagesResult = await sql`
      SELECT * FROM messages 
      WHERE conversation_id = ${id}
      ORDER BY created_at ASC;
    `;

    return {
      conversation: conversationResult.rows[0] as Conversation,
      messages: messagesResult.rows as Message[],
    };
  }

  public async getUserConversations(
    userId: string,
    limit: number = 50
  ): Promise<Conversation[]> {
    const result = await sql`
      SELECT * FROM conversations 
      WHERE user_id = ${userId} AND is_archived = FALSE
      ORDER BY updated_at DESC
      LIMIT ${limit};
    `;
    return result.rows as Conversation[];
  }

  public async updateConversation(
    id: string,
    data: {
      title?: string;
      isArchived?: boolean;
    }
  ): Promise<Conversation> {
    const result = await sql`
      UPDATE conversations 
      SET 
        title = COALESCE(${data.title}, title),
        is_archived = COALESCE(${data.isArchived}, is_archived)
      WHERE id = ${id}
      RETURNING *;
    `;
    return result.rows[0] as Conversation;
  }

  public async deleteConversation(id: string): Promise<void> {
    await sql`DELETE FROM conversations WHERE id = ${id};`;
  }

  // Message Operations
  public async createMessage(data: {
    conversationId: string;
    userId?: string;
    role: "user" | "assistant" | "system";
    content: string;
    metadata?: Record<string, any>;
  }): Promise<Message> {
    const result = await sql`
      INSERT INTO messages (conversation_id, user_id, role, content, metadata)
      VALUES (
        ${data.conversationId}, 
        ${data.userId || null}, 
        ${data.role}, 
        ${data.content}, 
        ${JSON.stringify(data.metadata || {})}
      )
      RETURNING *;
    `;

    // Update conversation's updated_at timestamp
    await sql`
      UPDATE conversations 
      SET updated_at = NOW() 
      WHERE id = ${data.conversationId};
    `;

    return result.rows[0] as Message;
  }

  public async getConversationMessages(
    conversationId: string
  ): Promise<Message[]> {
    const result = await sql`
      SELECT * FROM messages 
      WHERE conversation_id = ${conversationId}
      ORDER BY created_at ASC;
    `;
    return result.rows as Message[];
  }

  // API Usage Tracking
  public async logApiUsage(data: {
    userId?: string;
    model: string;
    tokens: number;
    cost?: number;
    endpoint: string;
    status: "success" | "error";
    errorCode?: string;
    errorMessage?: string;
  }): Promise<ApiUsage> {
    const result = await sql`
      INSERT INTO api_usage (
        user_id, model, tokens, cost, endpoint, status, error_code, error_message
      )
      VALUES (
        ${data.userId || null}, 
        ${data.model}, 
        ${data.tokens}, 
        ${data.cost || null}, 
        ${data.endpoint}, 
        ${data.status}, 
        ${data.errorCode || null}, 
        ${data.errorMessage || null}
      )
      RETURNING *;
    `;
    return result.rows[0] as ApiUsage;
  }

  public async getApiUsageStats(
    userId?: string,
    days: number = 30
  ): Promise<{
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    successRate: number;
  }> {
    let result;

    if (userId) {
      result = await sql`
        SELECT 
          COUNT(*) as total_requests,
          COALESCE(SUM(tokens), 0) as total_tokens,
          COALESCE(SUM(cost), 0) as total_cost,
          COALESCE(
            (COUNT(*) FILTER (WHERE status = 'success')::float / COUNT(*)) * 100, 
            0
          ) as success_rate
        FROM api_usage 
        WHERE user_id = ${userId}
        AND created_at >= NOW() - INTERVAL '${days} days';
      `;
    } else {
      result = await sql`
        SELECT 
          COUNT(*) as total_requests,
          COALESCE(SUM(tokens), 0) as total_tokens,
          COALESCE(SUM(cost), 0) as total_cost,
          COALESCE(
            (COUNT(*) FILTER (WHERE status = 'success')::float / COUNT(*)) * 100, 
            0
          ) as success_rate
        FROM api_usage 
        WHERE created_at >= NOW() - INTERVAL '${days} days';
      `;
    }

    const stats = result.rows[0];
    return {
      totalRequests: parseInt(stats.total_requests),
      totalTokens: parseInt(stats.total_tokens),
      totalCost: parseFloat(stats.total_cost),
      successRate: parseFloat(stats.success_rate),
    };
  }

  // System Configuration
  public async getSystemConfig(key: string): Promise<string | null> {
    const result = await sql`
      SELECT value FROM system_config WHERE key = ${key};
    `;
    return result.rows[0]?.value || null;
  }

  public async setSystemConfig(key: string, value: string): Promise<void> {
    await sql`
      INSERT INTO system_config (key, value)
      VALUES (${key}, ${value})
      ON CONFLICT (key) DO UPDATE SET value = ${value};
    `;
  }

  // Analytics and Monitoring
  public async getDatabaseStats(): Promise<{
    totalUsers: number;
    totalConversations: number;
    totalMessages: number;
    avgMessagesPerConversation: number;
  }> {
    const result = await sql`
      SELECT 
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM conversations WHERE is_archived = FALSE) as total_conversations,
        (SELECT COUNT(*) FROM messages) as total_messages,
        (SELECT COALESCE(AVG(message_count), 0) FROM (
          SELECT COUNT(*) as message_count 
          FROM messages 
          GROUP BY conversation_id
        ) as avg_calc) as avg_messages_per_conversation;
    `;

    const stats = result.rows[0];
    return {
      totalUsers: parseInt(stats.total_users),
      totalConversations: parseInt(stats.total_conversations),
      totalMessages: parseInt(stats.total_messages),
      avgMessagesPerConversation: parseFloat(
        stats.avg_messages_per_conversation
      ),
    };
  }
}

export const vercelDb = VercelDatabaseService.getInstance();
