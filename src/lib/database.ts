// Database configuration for Vercel Postgres
import { sql } from '@vercel/postgres';

export interface User {
  id: string;
  email: string;
  name: string;
  created_at: Date;
  updated_at: Date;
}

export interface Conversation {
  id: string;
  user_id: string;
  title: string;
  created_at: Date;
  updated_at: Date;
}

export interface Message {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
  created_at: Date;
}

export class DatabaseService {
  private static instance: DatabaseService;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Initialize database tables
  public async initializeTables(): Promise<void> {
    try {
      // Create users table
      await sql`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE NOT NULL,
          name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create conversations table
      await sql`
        CREATE TABLE IF NOT EXISTS conversations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES users(id) ON DELETE CASCADE,
          title VARCHAR(500) NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create messages table
      await sql`
        CREATE TABLE IF NOT EXISTS messages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
          role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
          content TEXT NOT NULL,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Create indexes for better performance
      await sql`
        CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
        CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
      `;

      console.log('✅ Database tables initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing database tables:', error);
      throw error;
    }
  }

  // User operations
  public async createUser(email: string, name: string): Promise<User> {
    try {
      const result = await sql`
        INSERT INTO users (email, name)
        VALUES (${email}, ${name})
        RETURNING *;
      `;
      return result.rows[0] as User;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  public async getUserByEmail(email: string): Promise<User | null> {
    try {
      const result = await sql`
        SELECT * FROM users WHERE email = ${email};
      `;
      return result.rows[0] as User || null;
    } catch (error) {
      console.error('Error getting user by email:', error);
      throw error;
    }
  }

  // Conversation operations
  public async createConversation(userId: string, title: string): Promise<Conversation> {
    try {
      const result = await sql`
        INSERT INTO conversations (user_id, title)
        VALUES (${userId}, ${title})
        RETURNING *;
      `;
      return result.rows[0] as Conversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  public async getConversationsByUser(userId: string): Promise<Conversation[]> {
    try {
      const result = await sql`
        SELECT * FROM conversations 
        WHERE user_id = ${userId}
        ORDER BY updated_at DESC;
      `;
      return result.rows as Conversation[];
    } catch (error) {
      console.error('Error getting conversations by user:', error);
      throw error;
    }
  }

  public async updateConversation(id: string, title: string): Promise<Conversation> {
    try {
      const result = await sql`
        UPDATE conversations 
        SET title = ${title}, updated_at = NOW()
        WHERE id = ${id}
        RETURNING *;
      `;
      return result.rows[0] as Conversation;
    } catch (error) {
      console.error('Error updating conversation:', error);
      throw error;
    }
  }

  public async deleteConversation(id: string): Promise<void> {
    try {
      await sql`
        DELETE FROM conversations WHERE id = ${id};
      `;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  // Message operations
  public async createMessage(
    conversationId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    metadata?: Record<string, any>
  ): Promise<Message> {
    try {
      const result = await sql`
        INSERT INTO messages (conversation_id, role, content, metadata)
        VALUES (${conversationId}, ${role}, ${content}, ${JSON.stringify(metadata || {})})
        RETURNING *;
      `;
      return result.rows[0] as Message;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  public async getMessagesByConversation(conversationId: string): Promise<Message[]> {
    try {
      const result = await sql`
        SELECT * FROM messages 
        WHERE conversation_id = ${conversationId}
        ORDER BY created_at ASC;
      `;
      return result.rows as Message[];
    } catch (error) {
      console.error('Error getting messages by conversation:', error);
      throw error;
    }
  }

  public async deleteMessage(id: string): Promise<void> {
    try {
      await sql`
        DELETE FROM messages WHERE id = ${id};
      `;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      await sql`SELECT 1;`;
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}

export const databaseService = DatabaseService.getInstance();