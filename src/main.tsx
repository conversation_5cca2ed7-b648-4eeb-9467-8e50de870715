import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { initializeApplication } from './config/startup'

// Initialize application configuration
initializeApplication().then((result) => {
  if (!result.success && result.environment === 'production') {
    console.error('❌ Failed to initialize application in production environment');
    // In production, we might want to show an error page
    // For now, we'll continue with the app but log the errors
  }
  
  // Render the application
  createRoot(document.getElementById("root")!).render(<App />);
}).catch((error) => {
  console.error('❌ Critical startup error:', error);
  // Still render the app but with degraded functionality
  createRoot(document.getElementById("root")!).render(<App />);
});
