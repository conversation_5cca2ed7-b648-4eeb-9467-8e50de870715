import { useState, useEffect, useCallback } from 'react';

interface UserBehavior {
  typingSpeed: 'slow' | 'normal' | 'fast';
  interactionFrequency: 'low' | 'medium' | 'high';
  preferredFeatures: string[];
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  sessionDuration: number;
  lastActivity: Date;
}

interface AdaptiveUIConfig {
  animationsEnabled: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';
  compactMode: boolean;
  highContrast: boolean;
  showTooltips: boolean;
  autoSuggestions: boolean;
  quickActions: boolean;
  neuralTriggers: boolean;
}

const defaultConfig: AdaptiveUIConfig = {
  animationsEnabled: true,
  animationSpeed: 'normal',
  compactMode: false,
  highContrast: false,
  showTooltips: true,
  autoSuggestions: true,
  quickActions: true,
  neuralTriggers: true,
};

export const useAdaptiveUI = () => {
  const [userBehavior, setUserBehavior] = useState<UserBehavior>({
    typingSpeed: 'normal',
    interactionFrequency: 'medium',
    preferredFeatures: [],
    timeOfDay: 'afternoon',
    sessionDuration: 0,
    lastActivity: new Date(),
  });

  const [uiConfig, setUiConfig] = useState<AdaptiveUIConfig>(defaultConfig);
  const [sessionStart] = useState<Date>(new Date());

  // Zeit des Tages bestimmen
  const getTimeOfDay = useCallback((): 'morning' | 'afternoon' | 'evening' | 'night' => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  }, []);

  // Tippgeschwindigkeit analysieren
  const analyzeTypingSpeed = useCallback((keystrokes: number, timeMs: number): 'slow' | 'normal' | 'fast' => {
    const wpm = (keystrokes / 5) / (timeMs / 60000); // Wörter pro Minute
    if (wpm < 30) return 'slow';
    if (wpm > 60) return 'fast';
    return 'normal';
  }, []);

  // Interaktionsfrequenz analysieren
  const analyzeInteractionFrequency = useCallback((interactions: number, timeMs: number): 'low' | 'medium' | 'high' => {
    const frequency = interactions / (timeMs / 1000 / 60); // Interaktionen pro Minute
    if (frequency < 2) return 'low';
    if (frequency > 5) return 'high';
    return 'medium';
  }, []);

  // UI-Konfiguration basierend auf Benutzerverhalten anpassen
  const updateUIConfig = useCallback((behavior: UserBehavior) => {
    const newConfig: AdaptiveUIConfig = { ...defaultConfig };

    // Animationen basierend auf Interaktionsfrequenz
    newConfig.animationsEnabled = behavior.interactionFrequency !== 'high';
    newConfig.animationSpeed = behavior.interactionFrequency === 'high' ? 'fast' : 
                              behavior.interactionFrequency === 'low' ? 'slow' : 'normal';

    // Kompaktmodus basierend auf bevorzugten Features
    newConfig.compactMode = behavior.preferredFeatures.length > 5;

    // Kontrast basierend auf Tageszeit
    newConfig.highContrast = behavior.timeOfDay === 'night' || behavior.timeOfDay === 'evening';

    // Tooltips basierend auf Erfahrung
    newConfig.showTooltips = behavior.sessionDuration < 300000; // 5 Minuten

    // Auto-Vorschläge basierend auf Tippgeschwindigkeit
    newConfig.autoSuggestions = behavior.typingSpeed !== 'fast';

    // Quick Actions basierend auf Interaktionsfrequenz
    newConfig.quickActions = behavior.interactionFrequency !== 'low';

    // Neuronale Trigger basierend auf Session-Dauer
    newConfig.neuralTriggers = behavior.sessionDuration > 120000; // 2 Minuten

    setUiConfig(newConfig);
  }, []);

  // Benutzerverhalten aktualisieren
  const updateBehavior = useCallback((updates: Partial<UserBehavior>) => {
    const newBehavior = { ...userBehavior, ...updates };
    newBehavior.sessionDuration = Date.now() - sessionStart.getTime();
    newBehavior.lastActivity = new Date();
    newBehavior.timeOfDay = getTimeOfDay();
    
    setUserBehavior(newBehavior);
    updateUIConfig(newBehavior);
  }, [userBehavior, sessionStart, getTimeOfDay, updateUIConfig]);

  // Tippereignis verfolgen
  const trackTyping = useCallback((keystrokes: number, timeMs: number) => {
    const typingSpeed = analyzeTypingSpeed(keystrokes, timeMs);
    updateBehavior({ typingSpeed });
  }, [analyzeTypingSpeed, updateBehavior]);

  // Interaktionsereignis verfolgen
  const trackInteraction = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - userBehavior.lastActivity.getTime();
    
    // Interaktionsfrequenz basierend auf Zeit seit letzter Interaktion
    if (timeSinceLastActivity < 5000) { // Weniger als 5 Sekunden
      updateBehavior({ interactionFrequency: 'high' });
    } else if (timeSinceLastActivity < 15000) { // Weniger als 15 Sekunden
      updateBehavior({ interactionFrequency: 'medium' });
    } else {
      updateBehavior({ interactionFrequency: 'low' });
    }
  }, [userBehavior.lastActivity, updateBehavior]);

  // Feature-Nutzung verfolgen
  const trackFeatureUsage = useCallback((feature: string) => {
    const preferredFeatures = [...userBehavior.preferredFeatures];
    if (!preferredFeatures.includes(feature)) {
      preferredFeatures.push(feature);
      // Nur die letzten 10 Features behalten
      if (preferredFeatures.length > 10) {
        preferredFeatures.shift();
      }
      updateBehavior({ preferredFeatures });
    }
  }, [userBehavior.preferredFeatures, updateBehavior]);

  // Automatische Updates basierend auf Tageszeit
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTimeOfDay = getTimeOfDay();
      if (currentTimeOfDay !== userBehavior.timeOfDay) {
        updateBehavior({ timeOfDay: currentTimeOfDay });
      }
    }, 60000); // Jede Minute prüfen

    return () => clearInterval(interval);
  }, [getTimeOfDay, userBehavior.timeOfDay, updateBehavior]);

  // Session-Dauer aktualisieren
  useEffect(() => {
    const interval = setInterval(() => {
      updateBehavior({ sessionDuration: Date.now() - sessionStart.getTime() });
    }, 1000); // Jede Sekunde aktualisieren

    return () => clearInterval(interval);
  }, [sessionStart, updateBehavior]);

  return {
    userBehavior,
    uiConfig,
    trackTyping,
    trackInteraction,
    trackFeatureUsage,
    updateBehavior,
  };
};