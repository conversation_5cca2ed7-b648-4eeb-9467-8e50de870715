import { useState, useEffect, useCallback } from 'react';

interface UserBehavior {
  sessionDuration: number;
  messageCount: number;
  averageMessageLength: number;
  preferredFeatures: {
    webSearch: boolean;
    deepThinking: boolean;
    tools: boolean;
    videoGeneration: boolean;
  };
  activityPattern: 'morning' | 'afternoon' | 'evening' | 'night';
  engagementLevel: 'low' | 'medium' | 'high';
  lastActive: Date;
}

interface NeuralTrigger {
  id: string;
  type: 'suggestion' | 'feature_highlight' | 'engagement' | 'personalization';
  title: string;
  description: string;
  action?: () => void;
  priority: number;
  conditions: (behavior: UserBehavior) => boolean;
}

const defaultBehavior: UserBehavior = {
  sessionDuration: 0,
  messageCount: 0,
  averageMessageLength: 0,
  preferredFeatures: {
    webSearch: false,
    deepThinking: false,
    tools: false,
    videoGeneration: false,
  },
  activityPattern: 'evening',
  engagementLevel: 'medium',
  lastActive: new Date(),
};

const neuralTriggers: NeuralTrigger[] = [
  {
    id: 'web_search_suggestion',
    type: 'suggestion',
    title: '🌐 Web-Suche aktivieren',
    description: 'Für aktuelle Informationen und Echtzeit-Daten',
    action: () => {},
    priority: 1,
    conditions: (behavior) => 
      behavior.messageCount > 3 && 
      !behavior.preferredFeatures.webSearch &&
      behavior.averageMessageLength > 50
  },
  {
    id: 'deep_thinking_suggestion',
    type: 'suggestion',
    title: '🧠 Deep Thinking nutzen',
    description: 'Für komplexe Analysen und detaillierte Lösungen',
    action: () => {},
    priority: 2,
    conditions: (behavior) => 
      behavior.messageCount > 5 && 
      !behavior.preferredFeatures.deepThinking &&
      behavior.averageMessageLength > 100
  },
  {
    id: 'video_generation_highlight',
    type: 'feature_highlight',
    title: '🎬 Video-Generation entdecken',
    description: 'Erstellen Sie beeindruckende Videos mit KI',
    action: () => {},
    priority: 3,
    conditions: (behavior) => 
      behavior.sessionDuration > 300000 && // 5 minutes
      !behavior.preferredFeatures.videoGeneration &&
      behavior.engagementLevel === 'high'
  },
  {
    id: 'engagement_boost',
    type: 'engagement',
    title: '✨ Erweiterte Funktionen',
    description: 'Entdecken Sie die volle Power von Spark AI',
    action: () => {},
    priority: 4,
    conditions: (behavior) => 
      behavior.sessionDuration > 600000 && // 10 minutes
      behavior.engagementLevel === 'high' &&
      behavior.messageCount > 10
  },
  {
    id: 'personalized_greeting',
    type: 'personalization',
    title: 'Willkommen zurück!',
    description: 'Basierend auf Ihrer letzten Nutzung',
    action: () => {},
    priority: 5,
    conditions: (behavior) => {
      const timeSinceLastActive = Date.now() - behavior.lastActive.getTime();
      return timeSinceLastActive > 86400000 && // 24 hours
             behavior.messageCount > 5;
    }
  }
];

export const useNeuralTriggers = () => {
  const [userBehavior, setUserBehavior] = useState<UserBehavior>(() => {
    const saved = localStorage.getItem('spark-user-behavior');
    return saved ? { ...defaultBehavior, ...JSON.parse(saved) } : defaultBehavior;
  });
  
  const [activeTriggers, setActiveTriggers] = useState<NeuralTrigger[]>([]);
  const [dismissedTriggers, setDismissedTriggers] = useState<string[]>(() => {
    const saved = localStorage.getItem('spark-dismissed-triggers');
    return saved ? JSON.parse(saved) : [];
  });

  // Benutzerverhalten speichern
  const saveBehavior = useCallback((behavior: UserBehavior) => {
    localStorage.setItem('spark-user-behavior', JSON.stringify(behavior));
  }, []);

  // Aktivitätszeitpunkt bestimmen
  const getActivityPattern = useCallback((): 'morning' | 'afternoon' | 'evening' | 'night' => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  }, []);

  // Engagement-Level berechnen
  const calculateEngagementLevel = useCallback((behavior: UserBehavior): 'low' | 'medium' | 'high' => {
    const sessionScore = Math.min(behavior.sessionDuration / 600000, 1); // Max 10 minutes
    const messageScore = Math.min(behavior.messageCount / 20, 1); // Max 20 messages
    const lengthScore = Math.min(behavior.averageMessageLength / 200, 1); // Max 200 chars
    
    const totalScore = (sessionScore + messageScore + lengthScore) / 3;
    
    if (totalScore > 0.7) return 'high';
    if (totalScore > 0.3) return 'medium';
    return 'low';
  }, []);

  // Benutzerinteraktion tracken
  const trackMessage = useCallback((content: string, features: UserBehavior['preferredFeatures']) => {
    setUserBehavior(prev => {
      const newBehavior = {
        ...prev,
        messageCount: prev.messageCount + 1,
        averageMessageLength: (prev.averageMessageLength * prev.messageCount + content.length) / (prev.messageCount + 1),
        preferredFeatures: {
          ...prev.preferredFeatures,
          ...features
        },
        activityPattern: getActivityPattern(),
        engagementLevel: calculateEngagementLevel({
          ...prev,
          messageCount: prev.messageCount + 1,
          averageMessageLength: (prev.averageMessageLength * prev.messageCount + content.length) / (prev.messageCount + 1)
        }),
        lastActive: new Date()
      };
      
      saveBehavior(newBehavior);
      return newBehavior;
    });
  }, [getActivityPattern, calculateEngagementLevel, saveBehavior]);

  // Session-Dauer aktualisieren
  const updateSessionDuration = useCallback(() => {
    setUserBehavior(prev => {
      const newBehavior = {
        ...prev,
        sessionDuration: Date.now() - prev.lastActive.getTime(),
        engagementLevel: calculateEngagementLevel(prev)
      };
      
      saveBehavior(newBehavior);
      return newBehavior;
    });
  }, [calculateEngagementLevel, saveBehavior]);

  // Trigger auswerten
  useEffect(() => {
    const interval = setInterval(updateSessionDuration, 5000); // Alle 5 Sekunden aktualisieren
    
    return () => clearInterval(interval);
  }, [updateSessionDuration]);

  // Aktive Trigger bestimmen
  useEffect(() => {
    const triggers = neuralTriggers.filter(trigger => 
      !dismissedTriggers.includes(trigger.id) && 
      trigger.conditions(userBehavior)
    ).sort((a, b) => a.priority - b.priority);
    
    setActiveTriggers(triggers);
  }, [userBehavior, dismissedTriggers]);

  // Trigger verwerfen
  const dismissTrigger = useCallback((triggerId: string) => {
    setDismissedTriggers(prev => {
      const newDismissed = [...prev, triggerId];
      localStorage.setItem('spark-dismissed-triggers', JSON.stringify(newDismissed));
      return newDismissed;
    });
  }, []);

  // Trigger zurücksetzen (für Testing)
  const resetTriggers = useCallback(() => {
    setDismissedTriggers([]);
    localStorage.removeItem('spark-dismissed-triggers');
  }, []);

  // Personalisierte Begrüßung basierend auf Aktivitätszeitpunkt
  const getPersonalizedGreeting = useCallback(() => {
    const pattern = userBehavior.activityPattern;
    const greetings = {
      morning: 'Guten Morgen! ☀️',
      afternoon: 'Guten Tag! 🌤️',
      evening: 'Guten Abend! 🌙',
      night: 'Gute Nacht! ✨'
    };
    
    return greetings[pattern];
  }, [userBehavior.activityPattern]);

  // Personalisierte Vorschläge basierend auf Verhalten
  const getPersonalizedSuggestions = useCallback(() => {
    const suggestions = [];
    
    if (userBehavior.engagementLevel === 'high' && !userBehavior.preferredFeatures.videoGeneration) {
      suggestions.push('Haben Sie schon unsere Video-Generation ausprobiert?');
    }
    
    if (userBehavior.averageMessageLength > 100 && !userBehavior.preferredFeatures.deepThinking) {
      suggestions.push('Für komplexe Anfragen empfehlen wir Deep Thinking.');
    }
    
    if (userBehavior.messageCount > 10 && !userBehavior.preferredFeatures.webSearch) {
      suggestions.push('Aktuelle Informationen? Aktivieren Sie die Web-Suche!');
    }
    
    return suggestions;
  }, [userBehavior]);

  return {
    userBehavior,
    activeTriggers,
    trackMessage,
    dismissTrigger,
    resetTriggers,
    getPersonalizedGreeting,
    getPersonalizedSuggestions
  };
};