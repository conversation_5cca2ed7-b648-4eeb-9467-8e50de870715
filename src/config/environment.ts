/**
 * Environment Variables Management System
 * Provides secure, validated, and type-safe access to environment variables
 * with support for different deployment environments
 */

import { securityManager } from './security';

// Environment types
export type Environment = 'development' | 'staging' | 'production';

// Configuration interfaces
export interface DatabaseConfig {
  url: string;
  prismaUrl: string;
  urlNonPooling: string;
  user?: string;
  host?: string;
  password?: string;
  database?: string;
}

export interface ApiConfig {
  endpoint: string;
  apiKey: string;
  version: string;
  defaultModel: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

export interface VideoConfig {
  model: string;
  maxWidth: number;
  maxHeight: number;
  maxDuration: number;
  maxSteps: number;
}

export interface SecurityConfig {
  rateLimitEnabled: boolean;
  rateLimitMaxRequests: number;
  rateLimitWindowMs: number;
  maxInputLength: number;
  logSecurityEvents: boolean;
  corsOrigins: string[];
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
}

export interface MonitoringConfig {
  debugMode: boolean;
  analyticsEnabled: boolean;
  errorTrackingEnabled: boolean;
  performanceMonitoring: boolean;
}

export interface EnvironmentConfig {
  environment: Environment;
  api: ApiConfig;
  database: DatabaseConfig;
  video: VideoConfig;
  security: SecurityConfig;
  retry: RetryConfig;
  monitoring: MonitoringConfig;
}

// Validation schemas
interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'url' | 'email';
  min?: number;
  max?: number;
  pattern?: RegExp;
  allowedValues?: string[];
}

const VALIDATION_RULES: Record<string, ValidationRule> = {
  // API Configuration
  VITE_API_ENDPOINT: { required: true, type: 'url' },
  VITE_API_KEY: { required: true, type: 'string', min: 32 },
  VITE_API_VERSION: { required: true, allowedValues: ['v4', 'v3'] },
  VITE_DEFAULT_MODEL: { required: true, type: 'string' },
  VITE_MAX_TOKENS: { required: true, type: 'number', min: 1, max: 32000 },
  VITE_TEMPERATURE: { required: true, type: 'number', min: 0, max: 2 },
  VITE_API_TIMEOUT: { type: 'number', min: 1000, max: 300000 },

  // Database Configuration
  POSTGRES_URL: { required: true, type: 'string' },
  POSTGRES_PRISMA_URL: { required: true, type: 'string' },
  POSTGRES_URL_NON_POOLING: { required: true, type: 'string' },

  // Video Configuration
  VITE_VIDEO_MODEL: { required: true, type: 'string' },
  VITE_VIDEO_MAX_WIDTH: { type: 'number', min: 256, max: 4096 },
  VITE_VIDEO_MAX_HEIGHT: { type: 'number', min: 256, max: 4096 },
  VITE_VIDEO_MAX_DURATION: { type: 'number', min: 1, max: 300 },
  VITE_VIDEO_MAX_STEPS: { type: 'number', min: 1, max: 1000 },

  // Security Configuration
  VITE_RATE_LIMIT_MAX_REQUESTS: { type: 'number', min: 1, max: 10000 },
  VITE_RATE_LIMIT_WINDOW_MS: { type: 'number', min: 1000, max: 3600000 },
  VITE_MAX_INPUT_LENGTH: { type: 'number', min: 100, max: 100000 },

  // Retry Configuration
  VITE_MAX_RETRIES: { type: 'number', min: 0, max: 10 },
  VITE_RETRY_DELAY: { type: 'number', min: 100, max: 30000 },
};

class EnvironmentManager {
  private static instance: EnvironmentManager;
  private config: EnvironmentConfig;
  private validationErrors: string[] = [];

  private constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): EnvironmentManager {
    if (!EnvironmentManager.instance) {
      EnvironmentManager.instance = new EnvironmentManager();
    }
    return EnvironmentManager.instance;
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfiguration(): EnvironmentConfig {
    const environment = this.determineEnvironment();

    return {
      environment,
      api: this.loadApiConfig(),
      database: this.loadDatabaseConfig(),
      video: this.loadVideoConfig(),
      security: this.loadSecurityConfig(),
      retry: this.loadRetryConfig(),
      monitoring: this.loadMonitoringConfig(),
    };
  }

  /**
   * Determine current environment
   */
  private determineEnvironment(): Environment {
    const nodeEnv = import.meta.env.NODE_ENV;
    const vercelEnv = import.meta.env.VERCEL_ENV;
    
    // Vercel environment detection
    if (vercelEnv === 'production') return 'production';
    if (vercelEnv === 'preview') return 'staging';
    
    // Node environment detection
    if (nodeEnv === 'production') return 'production';
    if (nodeEnv === 'test') return 'staging';
    
    return 'development';
  }

  /**
   * Load API configuration
   */
  private loadApiConfig(): ApiConfig {
    return {
      endpoint: this.getEnvVar('VITE_API_ENDPOINT', 'https://open.bigmodel.cn/api/paas/v4'),
      apiKey: this.getEnvVar('VITE_API_KEY', ''),
      version: this.getEnvVar('VITE_API_VERSION', 'v4'),
      defaultModel: this.getEnvVar('VITE_DEFAULT_MODEL', 'glm-4'),
      maxTokens: this.getEnvNumber('VITE_MAX_TOKENS', 4000),
      temperature: this.getEnvNumber('VITE_TEMPERATURE', 0.7),
      timeout: this.getEnvNumber('VITE_API_TIMEOUT', 30000),
    };
  }

  /**
   * Load database configuration
   */
  private loadDatabaseConfig(): DatabaseConfig {
    return {
      url: this.getEnvVar('DATABASE_URL', '') || this.getEnvVar('POSTGRES_URL', ''),
      prismaUrl: this.getEnvVar('POSTGRES_PRISMA_URL', '') || this.getEnvVar('DATABASE_URL', ''),
      urlNonPooling: this.getEnvVar('DATABASE_URL_UNPOOLED', '') || this.getEnvVar('POSTGRES_URL_NON_POOLING', ''),
      user: this.getEnvVar('POSTGRES_USER', ''),
      host: this.getEnvVar('POSTGRES_HOST', ''),
      password: this.getEnvVar('POSTGRES_PASSWORD', ''),
      database: this.getEnvVar('POSTGRES_DATABASE', ''),
    };
  }

  /**
   * Load video configuration
   */
  private loadVideoConfig(): VideoConfig {
    return {
      model: this.getEnvVar('VITE_VIDEO_MODEL', 'cogvideox-3'),
      maxWidth: this.getEnvNumber('VITE_VIDEO_MAX_WIDTH', 1920),
      maxHeight: this.getEnvNumber('VITE_VIDEO_MAX_HEIGHT', 1080),
      maxDuration: this.getEnvNumber('VITE_VIDEO_MAX_DURATION', 30),
      maxSteps: this.getEnvNumber('VITE_VIDEO_MAX_STEPS', 100),
    };
  }

  /**
   * Load security configuration
   */
  private loadSecurityConfig(): SecurityConfig {
    return {
      rateLimitEnabled: this.getEnvBoolean('VITE_RATE_LIMIT_ENABLED', true),
      rateLimitMaxRequests: this.getEnvNumber('VITE_RATE_LIMIT_MAX_REQUESTS', 60),
      rateLimitWindowMs: this.getEnvNumber('VITE_RATE_LIMIT_WINDOW_MS', 60000),
      maxInputLength: this.getEnvNumber('VITE_MAX_INPUT_LENGTH', 10000),
      logSecurityEvents: this.getEnvBoolean('VITE_LOG_SECURITY_EVENTS', false),
      corsOrigins: this.getEnvArray('VITE_CORS_ORIGINS', this.getDefaultCorsOrigins()),
    };
  }

  /**
   * Load retry configuration
   */
  private loadRetryConfig(): RetryConfig {
    return {
      maxRetries: this.getEnvNumber('VITE_MAX_RETRIES', 3),
      retryDelay: this.getEnvNumber('VITE_RETRY_DELAY', 2000),
      exponentialBackoff: this.getEnvBoolean('VITE_EXPONENTIAL_BACKOFF', true),
    };
  }

  /**
   * Load monitoring configuration
   */
  private loadMonitoringConfig(): MonitoringConfig {
    const isProduction = this.config?.environment === 'production';
    
    return {
      debugMode: this.getEnvBoolean('VITE_DEBUG_MODE', !isProduction),
      analyticsEnabled: this.getEnvBoolean('VITE_ANALYTICS_ENABLED', isProduction),
      errorTrackingEnabled: this.getEnvBoolean('VITE_ERROR_TRACKING_ENABLED', isProduction),
      performanceMonitoring: this.getEnvBoolean('VITE_PERFORMANCE_MONITORING', isProduction),
    };
  }

  /**
   * Get default CORS origins based on environment
   */
  private getDefaultCorsOrigins(): string[] {
    const environment = this.determineEnvironment();
    
    const baseOrigins = ['localhost', '127.0.0.1'];
    
    switch (environment) {
      case 'production':
        return [
          ...baseOrigins,
          'spark-ai-experience.vercel.app',
          'api.z.ai',
          'open.bigmodel.cn'
        ];
      case 'staging':
        return [
          ...baseOrigins,
          'spark-ai-experience-*.vercel.app',
          'api.z.ai',
          'open.bigmodel.cn'
        ];
      default:
        return baseOrigins;
    }
  }

  /**
   * Safely get environment variable with type conversion
   */
  private getEnvVar(key: string, defaultValue: string = ''): string {
    const value = import.meta.env[key];
    
    if (value === undefined || value === null) {
      if (VALIDATION_RULES[key]?.required) {
        this.validationErrors.push(`Required environment variable ${key} is missing`);
      }
      return defaultValue;
    }
    
    return String(value).trim();
  }

  /**
   * Get environment variable as number
   */
  private getEnvNumber(key: string, defaultValue: number): number {
    const value = this.getEnvVar(key);
    
    if (!value) return defaultValue;
    
    const parsed = parseFloat(value);
    if (isNaN(parsed)) {
      this.validationErrors.push(`Environment variable ${key} must be a valid number, got: ${value}`);
      return defaultValue;
    }
    
    return parsed;
  }

  /**
   * Get environment variable as boolean
   */
  private getEnvBoolean(key: string, defaultValue: boolean): boolean {
    const value = this.getEnvVar(key);
    
    if (!value) return defaultValue;
    
    const lowerValue = value.toLowerCase();
    return lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes';
  }

  /**
   * Get environment variable as array
   */
  private getEnvArray(key: string, defaultValue: string[]): string[] {
    const value = this.getEnvVar(key);
    
    if (!value) return defaultValue;
    
    try {
      return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
    } catch (error) {
      this.validationErrors.push(`Environment variable ${key} must be a comma-separated list`);
      return defaultValue;
    }
  }

  /**
   * Validate configuration against rules
   */
  private validateConfiguration(): void {
    this.validationErrors = [];

    // Validate each environment variable
    Object.entries(VALIDATION_RULES).forEach(([key, rule]) => {
      const value = import.meta.env[key];
      this.validateValue(key, value, rule);
    });

    // Custom validation logic
    this.performCustomValidation();

    // Log validation results
    if (this.validationErrors.length > 0) {
      console.error('❌ Environment Configuration Validation Errors:');
      this.validationErrors.forEach(error => console.error(`  - ${error}`));
      
      securityManager.logSecurityEvent('ENV_VALIDATION_FAILED', {
        errors: this.validationErrors,
        environment: this.config.environment
      });
    } else {
      console.log('✅ Environment configuration validated successfully');
    }
  }

  /**
   * Validate individual value against rule
   */
  private validateValue(key: string, value: any, rule: ValidationRule): void {
    // Check required
    if (rule.required && (!value || value.toString().trim() === '')) {
      this.validationErrors.push(`Required environment variable ${key} is missing or empty`);
      return;
    }

    if (!value) return; // Skip validation for optional empty values

    const stringValue = value.toString();

    // Type validation
    switch (rule.type) {
      case 'number':
        const numValue = parseFloat(stringValue);
        if (isNaN(numValue)) {
          this.validationErrors.push(`${key} must be a valid number`);
          return;
        }
        if (rule.min !== undefined && numValue < rule.min) {
          this.validationErrors.push(`${key} must be at least ${rule.min}`);
        }
        if (rule.max !== undefined && numValue > rule.max) {
          this.validationErrors.push(`${key} must be at most ${rule.max}`);
        }
        break;

      case 'url':
        try {
          new URL(stringValue);
        } catch {
          this.validationErrors.push(`${key} must be a valid URL`);
        }
        break;

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(stringValue)) {
          this.validationErrors.push(`${key} must be a valid email address`);
        }
        break;
    }

    // Length validation (only for string type, not for numbers)
    if (rule.type === 'string') {
      if (rule.min !== undefined && stringValue.length < rule.min) {
        this.validationErrors.push(`${key} must be at least ${rule.min} characters long`);
      }
      if (rule.max !== undefined && stringValue.length > rule.max) {
        this.validationErrors.push(`${key} must be at most ${rule.max} characters long`);
      }
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      this.validationErrors.push(`${key} does not match required pattern`);
    }

    // Allowed values validation
    if (rule.allowedValues && !rule.allowedValues.includes(stringValue)) {
      this.validationErrors.push(`${key} must be one of: ${rule.allowedValues.join(', ')}`);
    }
  }

  /**
   * Perform custom validation logic
   */
  private performCustomValidation(): void {
    // Validate API key format for Z.AI
    if (this.config.api.apiKey) {
      const apiKeyPattern = /^[a-zA-Z0-9]{32}\.[a-zA-Z0-9]{16}$/;
      if (!apiKeyPattern.test(this.config.api.apiKey)) {
        this.validationErrors.push('VITE_API_KEY must follow Z.AI format: 32chars.16chars');
      }
    }

    // Validate database URLs format
    if (this.config.database.url && !this.config.database.url.startsWith('postgres://')) {
      this.validationErrors.push('POSTGRES_URL must be a valid PostgreSQL connection string');
    }

    // Validate video dimensions
    if (this.config.video.maxWidth > 0 && this.config.video.maxHeight > 0) {
      const aspectRatio = this.config.video.maxWidth / this.config.video.maxHeight;
      if (aspectRatio < 0.1 || aspectRatio > 10) {
        this.validationErrors.push('Video aspect ratio must be reasonable (between 0.1 and 10)');
      }
    }

    // Validate rate limiting configuration
    if (this.config.security.rateLimitEnabled) {
      if (this.config.security.rateLimitMaxRequests <= 0) {
        this.validationErrors.push('Rate limit max requests must be greater than 0');
      }
      if (this.config.security.rateLimitWindowMs < 1000) {
        this.validationErrors.push('Rate limit window must be at least 1000ms');
      }
    }

    // Environment-specific validation
    if (this.config.environment === 'production') {
      if (!this.config.api.apiKey) {
        this.validationErrors.push('API key is required in production environment');
      }
      if (!this.config.database.url) {
        this.validationErrors.push('Database URL is required in production environment');
      }
      if (this.config.monitoring.debugMode) {
        console.warn('⚠️ Debug mode is enabled in production environment');
      }
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): EnvironmentConfig {
    return { ...this.config };
  }

  /**
   * Get configuration for specific section
   */
  public getApiConfig(): ApiConfig {
    return { ...this.config.api };
  }

  public getDatabaseConfig(): DatabaseConfig {
    return { ...this.config.database };
  }

  public getVideoConfig(): VideoConfig {
    return { ...this.config.video };
  }

  public getSecurityConfig(): SecurityConfig {
    return { ...this.config.security };
  }

  public getRetryConfig(): RetryConfig {
    return { ...this.config.retry };
  }

  public getMonitoringConfig(): MonitoringConfig {
    return { ...this.config.monitoring };
  }

  /**
   * Check if configuration is valid
   */
  public isValid(): boolean {
    return this.validationErrors.length === 0;
  }

  /**
   * Get validation errors
   */
  public getValidationErrors(): string[] {
    return [...this.validationErrors];
  }

  /**
   * Check if running in production
   */
  public isProduction(): boolean {
    return this.config.environment === 'production';
  }

  /**
   * Check if running in development
   */
  public isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  /**
   * Check if running in staging
   */
  public isStaging(): boolean {
    return this.config.environment === 'staging';
  }

  /**
   * Get environment name
   */
  public getEnvironment(): Environment {
    return this.config.environment;
  }

  /**
   * Reload configuration (useful for testing)
   */
  public reloadConfiguration(): void {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Get configuration summary for logging
   */
  public getConfigSummary(): Record<string, any> {
    return {
      environment: this.config.environment,
      apiConfigured: Boolean(this.config.api.apiKey),
      databaseConfigured: Boolean(this.config.database.url),
      debugMode: this.config.monitoring.debugMode,
      rateLimitEnabled: this.config.security.rateLimitEnabled,
      validationErrors: this.validationErrors.length,
    };
  }
}

// Export singleton instance
export const environmentManager = EnvironmentManager.getInstance();

// Export types
export type {
  EnvironmentConfig,
  ApiConfig,
  DatabaseConfig,
  VideoConfig,
  SecurityConfig as EnvSecurityConfig,
  RetryConfig,
  MonitoringConfig,
};