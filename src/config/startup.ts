/**
 * Application Startup Configuration
 * Initializes and validates environment configuration at application startup
 */

import { environmentManager } from "./environment";
import { configValidator, logStatus } from "./validation";
import { securityManager } from "./security";
import { vercelDb } from "../lib/database-integration";

export interface StartupResult {
  success: boolean;
  environment: string;
  configValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Initialize application configuration and perform startup validation
 */
export async function initializeApplication(): Promise<StartupResult> {
  console.log("🚀 Initializing Spark AI Experience...");

  try {
    // Load and validate environment configuration
    const environment = environmentManager.getEnvironment();
    console.log(`📍 Environment: ${environment}`);

    // Perform comprehensive validation
    const validation = configValidator.validateConfiguration();
    const healthCheck = configValidator.performHealthCheck();

    // Log configuration status
    logStatus();

    // Check for critical errors
    const criticalErrors = validation.errors.filter(
      (error) =>
        error.includes("Required") ||
        error.includes("API key") ||
        error.includes("Database")
    );

    if (criticalErrors.length > 0 && environmentManager.isProduction()) {
      console.error(
        "❌ Critical configuration errors in production environment"
      );
      return {
        success: false,
        environment,
        configValid: false,
        errors: validation.errors,
        warnings: validation.warnings,
      };
    }

    // Check database connection (only if database is configured)
    let databaseHealth = null;
    const dbConfig = environmentManager.getDatabaseConfig();
    if (dbConfig.url) {
      try {
        console.log("📡 Checking database connection...");
        databaseHealth = await vercelDb.healthCheck();
        if (databaseHealth.connected) {
          console.log(`✅ Database connected (${databaseHealth.latency}ms)`);
        } else {
          console.warn(`⚠️ Database connection failed: ${databaseHealth.error}`);
        }
      } catch (error) {
        console.warn("⚠️ Database health check failed:", error);
      }
    }

    // Log startup summary
    const configSummary = environmentManager.getConfigSummary();
    console.log("✅ Application initialized successfully", {
      environment,
      healthStatus: healthCheck.status,
      configValid: validation.isValid,
      databaseConnected: databaseHealth?.connected || false,
      ...configSummary,
    });

    // Initialize security manager cleanup
    if (typeof window !== "undefined") {
      // Clean up rate limits every 5 minutes
      setInterval(() => {
        securityManager.cleanupRateLimits();
      }, 5 * 60 * 1000);
    }

    return {
      success: true,
      environment,
      configValid: validation.isValid,
      errors: validation.errors,
      warnings: validation.warnings,
    };
  } catch (error) {
    console.error("❌ Failed to initialize application:", error);

    return {
      success: false,
      environment: "unknown",
      configValid: false,
      errors: [
        error instanceof Error ? error.message : "Unknown initialization error",
      ],
      warnings: [],
    };
  }
}

/**
 * Get application readiness status
 */
export function getApplicationReadiness(): {
  ready: boolean;
  status: string;
  details: Record<string, any>;
} {
  try {
    const healthCheck = configValidator.performHealthCheck();
    const config = environmentManager.getConfig();

    const ready =
      healthCheck.status !== "error" &&
      (environmentManager.isDevelopment() ||
        (config.api.apiKey && config.database.url));

    return {
      ready,
      status: healthCheck.status,
      details: {
        environment: environmentManager.getEnvironment(),
        apiConfigured: !!config.api.apiKey,
        databaseConfigured: !!config.database.url,
        healthChecks: healthCheck.checks.length,
        passedChecks: healthCheck.checks.filter((c) => c.status === "pass")
          .length,
      },
    };
  } catch (error) {
    return {
      ready: false,
      status: "error",
      details: {
        error: error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
}

/**
 * Validate environment for specific deployment target
 */
export function validateForDeployment(
  target: "development" | "staging" | "production"
): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const config = environmentManager.getConfig();

  // Common validation
  if (!config.api.endpoint) {
    errors.push("API endpoint is required");
  }

  // Environment-specific validation
  switch (target) {
    case "production":
      if (!config.api.apiKey) {
        errors.push("API key is required for production deployment");
      }
      if (!config.database.url) {
        errors.push("Database URL is required for production deployment");
      }
      if (config.monitoring.debugMode) {
        warnings.push("Debug mode should be disabled in production");
      }
      if (!config.monitoring.analyticsEnabled) {
        warnings.push("Analytics should be enabled in production");
      }
      if (!config.monitoring.errorTrackingEnabled) {
        warnings.push("Error tracking should be enabled in production");
      }
      if (config.security.corsOrigins.includes("*")) {
        errors.push("Wildcard CORS origins are not allowed in production");
      }
      break;

    case "staging":
      if (!config.api.apiKey) {
        warnings.push("API key should be configured for staging environment");
      }
      if (!config.database.url) {
        warnings.push(
          "Database URL should be configured for staging environment"
        );
      }
      break;

    case "development":
      // More relaxed validation for development
      if (!config.api.apiKey) {
        warnings.push("API key not configured - using mock responses");
      }
      break;
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

// Auto-initialize when module is imported (except in test environment)
if (typeof window !== "undefined" && import.meta.env.NODE_ENV !== "test") {
  initializeApplication().catch((error) => {
    console.error("Failed to auto-initialize application:", error);
  });
}
