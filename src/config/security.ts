// Security Configuration and Utilities
export interface SecurityConfig {
  rateLimiting: {
    enabled: boolean;
    maxRequests: number;
    windowMs: number;
    skipSuccessfulRequests: boolean;
  };
  inputValidation: {
    enabled: boolean;
    maxInputLength: number;
    allowedCharacters: RegExp;
    sanitizeHtml: boolean;
  };
  apiSecurity: {
    validateApiKey: boolean;
    encryptApiCalls: boolean;
    logSecurityEvents: boolean;
  };
}

class SecurityManager {
  private static instance: SecurityManager;
  private config: SecurityConfig;
  private requestCounts: Map<string, { count: number; resetTime: number }> =
    new Map();

  private constructor() {
    this.config = {
      rateLimiting: {
        enabled: import.meta.env.VITE_RATE_LIMIT_ENABLED !== "false",
        maxRequests:
          parseInt(import.meta.env.VITE_RATE_LIMIT_MAX_REQUESTS) || 60,
        windowMs: parseInt(import.meta.env.VITE_RATE_LIMIT_WINDOW_MS) || 60000, // 1 minute
        skipSuccessfulRequests: false,
      },
      inputValidation: {
        enabled: true,
        maxInputLength:
          parseInt(import.meta.env.VITE_MAX_INPUT_LENGTH) || 10000,
        allowedCharacters: /^[\s\S]*$/, // Allow all characters by default, but sanitize
        sanitizeHtml: true,
      },
      apiSecurity: {
        validateApiKey: true,
        encryptApiCalls: true,
        logSecurityEvents:
          import.meta.env.VITE_LOG_SECURITY_EVENTS === "true" || false,
      },
    };
  }

  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  /**
   * Validate and sanitize user input
   */
  public sanitizeInput(input: string): string {
    if (!this.config.inputValidation.enabled) {
      return input;
    }

    // Check input length
    if (input.length > this.config.inputValidation.maxInputLength) {
      throw new Error(
        `Input too long. Maximum ${this.config.inputValidation.maxInputLength} characters allowed.`
      );
    }

    // Basic HTML sanitization
    if (this.config.inputValidation.sanitizeHtml) {
      input = this.sanitizeHtml(input);
    }

    // Remove potentially dangerous patterns
    input = this.removeDangerousPatterns(input);

    return input.trim();
  }

  /**
   * Basic HTML sanitization
   */
  private sanitizeHtml(input: string): string {
    // Remove script tags and their content
    input = input.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );

    // Remove dangerous HTML attributes
    input = input.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "");
    input = input.replace(/\s*javascript\s*:/gi, "");

    // Remove potentially dangerous HTML tags
    const dangerousTags = [
      "iframe",
      "object",
      "embed",
      "form",
      "input",
      "button",
    ];
    dangerousTags.forEach((tag) => {
      const regex = new RegExp(`<\\/?${tag}\\b[^>]*>`, "gi");
      input = input.replace(regex, "");
    });

    return input;
  }

  /**
   * Remove dangerous patterns that could be used for injection attacks
   */
  private removeDangerousPatterns(input: string): string {
    // Remove SQL injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    ];

    sqlPatterns.forEach((pattern) => {
      input = input.replace(pattern, "");
    });

    // Remove command injection patterns
    const cmdPatterns = [
      /(\||&|;|`|\$\(|\${)/g,
      /(rm\s|del\s|format\s|shutdown\s)/gi,
    ];

    cmdPatterns.forEach((pattern) => {
      input = input.replace(pattern, "");
    });

    return input;
  }

  /**
   * Check rate limiting for a given identifier (IP, user ID, etc.)
   */
  public checkRateLimit(identifier: string): {
    allowed: boolean;
    resetTime?: number;
  } {
    if (!this.config.rateLimiting.enabled) {
      return { allowed: true };
    }

    const now = Date.now();
    const userLimit = this.requestCounts.get(identifier);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or create new limit
      this.requestCounts.set(identifier, {
        count: 1,
        resetTime: now + this.config.rateLimiting.windowMs,
      });
      return { allowed: true };
    }

    // Check if limit would be exceeded BEFORE incrementing
    if (userLimit.count >= this.config.rateLimiting.maxRequests) {
      return { allowed: false, resetTime: userLimit.resetTime };
    }

    // Increment count only if allowed
    userLimit.count++;
    this.requestCounts.set(identifier, userLimit);

    return { allowed: true };
  }

  /**
   * Validate API key format and structure
   */
  public validateApiKey(apiKey: string): boolean {
    if (!this.config.apiSecurity.validateApiKey) {
      return true;
    }

    // Check if API key exists
    if (!apiKey || apiKey.trim().length === 0) {
      return false;
    }

    // Check API key format (Z.AI format: alphanumeric.alphanumeric)
    const apiKeyPattern = /^[a-zA-Z0-9]{32}\.[a-zA-Z0-9]{16}$/;
    return apiKeyPattern.test(apiKey);
  }

  /**
   * Log security events
   */
  public logSecurityEvent(event: string, details: any): void {
    if (!this.config.apiSecurity.logSecurityEvents) {
      return;
    }

    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details: this.sanitizeLogDetails(details),
      userAgent:
        typeof navigator !== "undefined" ? navigator.userAgent : "unknown",
    };

    console.warn("🔒 Security Event:", logEntry);
  }

  /**
   * Sanitize log details to prevent sensitive information leakage
   */
  private sanitizeLogDetails(details: any): any {
    if (typeof details !== "object" || details === null) {
      return details;
    }

    const sanitized = { ...details };

    // Remove sensitive fields
    const sensitiveFields = ["apiKey", "password", "token", "secret", "key"];
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = "***masked***";
      }
    });

    return sanitized;
  }

  /**
   * Generate a secure request ID
   */
  public generateSecureRequestId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 15);
    return `req_${timestamp}_${random}`;
  }

  /**
   * Clean up old rate limit entries
   */
  public cleanupRateLimits(): void {
    const now = Date.now();
    for (const [identifier, limit] of this.requestCounts.entries()) {
      if (now > limit.resetTime) {
        this.requestCounts.delete(identifier);
      }
    }
  }

  /**
   * Reset rate limits for testing purposes
   */
  public resetRateLimits(): void {
    this.requestCounts.clear();
  }

  /**
   * Override configuration for testing purposes
   */
  public setTestConfig(testConfig: Partial<SecurityConfig>): void {
    this.config = {
      ...this.config,
      ...testConfig,
      rateLimiting: {
        ...this.config.rateLimiting,
        ...testConfig.rateLimiting,
      },
      inputValidation: {
        ...this.config.inputValidation,
        ...testConfig.inputValidation,
      },
      apiSecurity: {
        ...this.config.apiSecurity,
        ...testConfig.apiSecurity,
      },
    };
    this.requestCounts.clear();
  }

  /**
   * Get security configuration
   */
  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * Reinitialize configuration (for testing purposes)
   */
  public reinitializeConfig(): void {
    this.config = {
      rateLimiting: {
        enabled: import.meta.env.VITE_RATE_LIMIT_ENABLED !== "false",
        maxRequests:
          parseInt(import.meta.env.VITE_RATE_LIMIT_MAX_REQUESTS) || 60,
        windowMs: parseInt(import.meta.env.VITE_RATE_LIMIT_WINDOW_MS) || 60000, // 1 minute
        skipSuccessfulRequests: false,
      },
      inputValidation: {
        enabled: true,
        maxInputLength:
          parseInt(import.meta.env.VITE_MAX_INPUT_LENGTH) || 10000,
        allowedCharacters: /^[\s\S]*$/, // Allow all characters by default, but sanitize
        sanitizeHtml: true,
      },
      apiSecurity: {
        validateApiKey: true,
        encryptApiCalls: true,
        logSecurityEvents:
          import.meta.env.VITE_LOG_SECURITY_EVENTS === "true" || false,
      },
    };

    // Clear existing rate limits when reinitializing
    this.requestCounts.clear();
  }
}

export const securityManager = SecurityManager.getInstance();

// Clean up rate limits every 5 minutes
if (typeof window !== "undefined") {
  setInterval(() => {
    securityManager.cleanupRateLimits();
  }, 5 * 60 * 1000);
}
