import { securityManager } from './security';
import { environmentManager } from './environment';
import type { ApiConfig } from './environment';

class ConfigManager {
  private static instance: ConfigManager;
  private config: ApiConfig;

  private constructor() {
    // Get configuration from environment manager
    this.config = environmentManager.getApiConfig();
    
    // Validate critical configuration
    this.validateConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private validateConfig(): void {
    // Validate API key format
    if (this.config.apiKey && !securityManager.validateApiKey(this.config.apiKey)) {
      securityManager.logSecurityEvent('INVALID_API_KEY_FORMAT', { 
        keyLength: this.config.apiKey.length 
      });
      console.warn('⚠️ API Key format appears invalid. Expected format: 32chars.16chars');
    }

    if (!this.config.apiKey) {
      console.warn('⚠️ API Key not configured. Using mock responses.');
      securityManager.logSecurityEvent('API_KEY_NOT_CONFIGURED', {});
    }

    // Validate endpoint URL
    if (!this.isValidUrl(this.config.endpoint)) {
      securityManager.logSecurityEvent('INVALID_API_ENDPOINT', { endpoint: this.config.endpoint });
      console.error('❌ Invalid API endpoint URL');
    }

    // Get monitoring config for debug mode
    const monitoringConfig = environmentManager.getMonitoringConfig();
    
    if (monitoringConfig.debugMode) {
      console.log('🔧 Debug mode enabled. API Config:', {
        ...this.config,
        apiKey: this.config.apiKey ? '***masked***' : 'not set'
      });
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  public getConfig(): ApiConfig {
    return { ...this.config };
  }

  public isConfigured(): boolean {
    const configured = Boolean(this.config.apiKey && this.config.endpoint);
    
    if (!configured) {
      securityManager.logSecurityEvent('API_NOT_CONFIGURED', {
        hasApiKey: Boolean(this.config.apiKey),
        hasEndpoint: Boolean(this.config.endpoint)
      });
    }
    
    return configured;
  }

  public updateConfig(updates: Partial<ApiConfig>): void {
    // Log configuration changes
    securityManager.logSecurityEvent('CONFIG_UPDATE', {
      updatedFields: Object.keys(updates)
    });

    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }

  /**
   * Check if origin is allowed for CORS
   */
  public isOriginAllowed(origin: string): boolean {
    if (!origin) return false;
    
    // Get CORS origins from security config
    const securityConfig = environmentManager.getSecurityConfig();
    
    // Extract hostname from origin URL
    let hostname: string;
    try {
      const url = new URL(origin);
      hostname = url.hostname;
    } catch {
      // If not a valid URL, treat as hostname
      hostname = origin;
    }
    
    return securityConfig.corsOrigins.some(allowedOrigin => {
      // Handle wildcard patterns like *.vercel.app
      if (allowedOrigin.includes('*')) {
        const pattern = allowedOrigin.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(hostname);
      }
      
      // Exact match or subdomain match
      return hostname === allowedOrigin || hostname.endsWith(`.${allowedOrigin}`);
    });
  }

  /**
   * Get CORS headers for API responses
   */
  public getCorsHeaders(origin?: string): Record<string, string> {
    const monitoringConfig = environmentManager.getMonitoringConfig();
    
    const headers: Record<string, string> = {
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Request-ID',
      'Access-Control-Max-Age': '86400',
      'Access-Control-Allow-Credentials': 'true'
    };

    // Set origin if provided and allowed
    if (origin && this.isOriginAllowed(origin)) {
      headers['Access-Control-Allow-Origin'] = origin;
    } else if (monitoringConfig.debugMode) {
      // In debug mode, allow localhost origins
      if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
        headers['Access-Control-Allow-Origin'] = origin;
      }
    }

    return headers;
  }
}

export const apiConfig = ConfigManager.getInstance();
export type { ApiConfig };