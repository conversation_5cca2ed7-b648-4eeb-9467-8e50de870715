/**
 * Environment Variables Validation Utilities
 * Provides validation functions and health checks for environment configuration
 */

import { environmentManager } from './environment';
import { securityManager } from './security';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: ValidationSummary;
}

export interface ValidationSummary {
  environment: string;
  totalVariables: number;
  validVariables: number;
  missingRequired: number;
  invalidFormat: number;
  securityIssues: number;
}

export interface HealthCheckResult {
  status: 'healthy' | 'warning' | 'error';
  checks: HealthCheck[];
  timestamp: Date;
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'warn' | 'fail';
  message: string;
  details?: any;
}

class ConfigurationValidator {
  private static instance: ConfigurationValidator;

  private constructor() {}

  public static getInstance(): ConfigurationValidator {
    if (!ConfigurationValidator.instance) {
      ConfigurationValidator.instance = new ConfigurationValidator();
    }
    return ConfigurationValidator.instance;
  }

  /**
   * Perform comprehensive validation of environment configuration
   */
  public validateConfiguration(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Get validation errors from environment manager
    const envErrors = environmentManager.getValidationErrors();
    errors.push(...envErrors);

    // Perform additional validation checks
    const additionalChecks = this.performAdditionalValidation();
    errors.push(...additionalChecks.errors);
    warnings.push(...additionalChecks.warnings);

    // Generate summary
    const summary = this.generateValidationSummary(errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      summary
    };
  }

  /**
   * Perform additional validation beyond basic environment variable validation
   */
  private performAdditionalValidation(): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const config = environmentManager.getConfig();

    // API Configuration Validation
    if (config.api.apiKey) {
      if (!this.validateApiKeyStrength(config.api.apiKey)) {
        warnings.push('API key appears to be a test or weak key');
      }
    }

    // Database Configuration Validation
    if (config.database.url) {
      if (!this.validateDatabaseConnection(config.database.url)) {
        errors.push('Database connection string appears invalid');
      }
    }

    // Security Configuration Validation
    if (config.security.rateLimitEnabled) {
      if (config.security.rateLimitMaxRequests > 1000) {
        warnings.push('Rate limit is set very high, consider reducing for better security');
      }
    }

    // CORS Configuration Validation
    if (config.security.corsOrigins.includes('*')) {
      if (environmentManager.isProduction()) {
        errors.push('Wildcard CORS origin (*) is not allowed in production');
      } else {
        warnings.push('Wildcard CORS origin (*) detected, ensure this is intentional');
      }
    }

    // Environment-Specific Validation
    if (environmentManager.isProduction()) {
      if (config.monitoring.debugMode) {
        warnings.push('Debug mode is enabled in production environment');
      }
      if (!config.monitoring.analyticsEnabled) {
        warnings.push('Analytics is disabled in production environment');
      }
      if (!config.monitoring.errorTrackingEnabled) {
        warnings.push('Error tracking is disabled in production environment');
      }
    }

    // Video Configuration Validation
    const aspectRatio = config.video.maxWidth / config.video.maxHeight;
    if (aspectRatio < 0.5 || aspectRatio > 3) {
      warnings.push('Video aspect ratio is unusual, verify this is intentional');
    }

    return { errors, warnings };
  }

  /**
   * Validate API key strength (basic heuristics)
   */
  private validateApiKeyStrength(apiKey: string): boolean {
    // Check for test patterns
    const testPatterns = [
      /test/i,
      /demo/i,
      /example/i,
      /placeholder/i,
      /^[a-f0-9]+$/i, // Only hex characters (might be weak)
      /^[0-9]+$/,     // Only numbers (definitely weak)
    ];

    return !testPatterns.some(pattern => pattern.test(apiKey));
  }

  /**
   * Validate database connection string format
   */
  private validateDatabaseConnection(connectionString: string): boolean {
    try {
      // Basic PostgreSQL connection string validation
      const postgresPattern = /^postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)$/;
      return postgresPattern.test(connectionString);
    } catch {
      return false;
    }
  }

  /**
   * Generate validation summary
   */
  private generateValidationSummary(errors: string[], warnings: string[]): ValidationSummary {
    const config = environmentManager.getConfig();
    
    // Count variables (approximate)
    const totalVariables = Object.keys(import.meta.env).filter(key => 
      key.startsWith('VITE_') || key.startsWith('POSTGRES_')
    ).length;

    return {
      environment: config.environment,
      totalVariables,
      validVariables: totalVariables - errors.length,
      missingRequired: errors.filter(error => error.includes('missing')).length,
      invalidFormat: errors.filter(error => error.includes('format') || error.includes('pattern')).length,
      securityIssues: warnings.filter(warning => 
        warning.includes('security') || warning.includes('CORS') || warning.includes('debug')
      ).length,
    };
  }

  /**
   * Perform health check of the configuration system
   */
  public performHealthCheck(): HealthCheckResult {
    const checks: HealthCheck[] = [];
    let overallStatus: 'healthy' | 'warning' | 'error' = 'healthy';

    // Environment Detection Check
    try {
      const environment = environmentManager.getEnvironment();
      checks.push({
        name: 'Environment Detection',
        status: 'pass',
        message: `Environment detected as: ${environment}`,
        details: { environment }
      });
    } catch (error) {
      checks.push({
        name: 'Environment Detection',
        status: 'fail',
        message: 'Failed to detect environment',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      overallStatus = 'error';
    }

    // Configuration Loading Check
    try {
      const config = environmentManager.getConfig();
      checks.push({
        name: 'Configuration Loading',
        status: 'pass',
        message: 'Configuration loaded successfully',
        details: environmentManager.getConfigSummary()
      });
    } catch (error) {
      checks.push({
        name: 'Configuration Loading',
        status: 'fail',
        message: 'Failed to load configuration',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      overallStatus = 'error';
    }

    // Validation Check
    const validationResult = this.validateConfiguration();
    if (validationResult.isValid) {
      checks.push({
        name: 'Configuration Validation',
        status: 'pass',
        message: 'All configuration variables are valid',
        details: validationResult.summary
      });
    } else {
      checks.push({
        name: 'Configuration Validation',
        status: validationResult.errors.length > 0 ? 'fail' : 'warn',
        message: `Found ${validationResult.errors.length} errors and ${validationResult.warnings.length} warnings`,
        details: {
          errors: validationResult.errors,
          warnings: validationResult.warnings,
          summary: validationResult.summary
        }
      });
      if (validationResult.errors.length > 0) {
        overallStatus = 'error';
      } else if (overallStatus === 'healthy') {
        overallStatus = 'warning';
      }
    }

    // API Configuration Check
    const apiConfig = environmentManager.getApiConfig();
    if (apiConfig.apiKey) {
      checks.push({
        name: 'API Configuration',
        status: 'pass',
        message: 'API key is configured',
        details: { 
          endpoint: apiConfig.endpoint,
          version: apiConfig.version,
          model: apiConfig.defaultModel
        }
      });
    } else {
      checks.push({
        name: 'API Configuration',
        status: environmentManager.isDevelopment() ? 'warn' : 'fail',
        message: 'API key is not configured',
        details: { mockMode: environmentManager.isDevelopment() }
      });
      if (!environmentManager.isDevelopment() && overallStatus !== 'error') {
        overallStatus = 'error';
      } else if (overallStatus === 'healthy') {
        overallStatus = 'warning';
      }
    }

    // Database Configuration Check
    const dbConfig = environmentManager.getDatabaseConfig();
    if (dbConfig.url) {
      checks.push({
        name: 'Database Configuration',
        status: 'pass',
        message: 'Database connection string is configured',
        details: { hasUrl: true, hasPrismaUrl: Boolean(dbConfig.prismaUrl) }
      });
    } else {
      checks.push({
        name: 'Database Configuration',
        status: environmentManager.isDevelopment() ? 'warn' : 'fail',
        message: 'Database connection string is not configured',
        details: { environment: environmentManager.getEnvironment() }
      });
      if (!environmentManager.isDevelopment() && overallStatus !== 'error') {
        overallStatus = 'error';
      } else if (overallStatus === 'healthy') {
        overallStatus = 'warning';
      }
    }

    // Security Configuration Check
    const securityConfig = environmentManager.getSecurityConfig();
    checks.push({
      name: 'Security Configuration',
      status: 'pass',
      message: 'Security configuration loaded',
      details: {
        rateLimitEnabled: securityConfig.rateLimitEnabled,
        corsConfigured: securityConfig.corsOrigins.length > 0,
        securityLogging: securityConfig.logSecurityEvents
      }
    });

    return {
      status: overallStatus,
      checks,
      timestamp: new Date()
    };
  }

  /**
   * Generate configuration report for debugging
   */
  public generateConfigurationReport(): string {
    const healthCheck = this.performHealthCheck();
    const validation = this.validateConfiguration();
    const config = environmentManager.getConfig();

    let report = '# Configuration Report\n\n';
    
    // Health Check Summary
    report += `## Health Check Status: ${healthCheck.status.toUpperCase()}\n`;
    report += `Generated: ${healthCheck.timestamp.toISOString()}\n\n`;

    // Environment Info
    report += `## Environment Information\n`;
    report += `- Environment: ${config.environment}\n`;
    report += `- Debug Mode: ${config.monitoring.debugMode}\n`;
    report += `- Analytics: ${config.monitoring.analyticsEnabled}\n\n`;

    // Validation Summary
    report += `## Validation Summary\n`;
    report += `- Valid: ${validation.isValid ? 'Yes' : 'No'}\n`;
    report += `- Errors: ${validation.errors.length}\n`;
    report += `- Warnings: ${validation.warnings.length}\n`;
    report += `- Total Variables: ${validation.summary.totalVariables}\n\n`;

    // Health Checks
    report += `## Health Checks\n`;
    healthCheck.checks.forEach(check => {
      const status = check.status === 'pass' ? '✅' : check.status === 'warn' ? '⚠️' : '❌';
      report += `${status} **${check.name}**: ${check.message}\n`;
    });
    report += '\n';

    // Errors
    if (validation.errors.length > 0) {
      report += `## Errors\n`;
      validation.errors.forEach(error => {
        report += `- ❌ ${error}\n`;
      });
      report += '\n';
    }

    // Warnings
    if (validation.warnings.length > 0) {
      report += `## Warnings\n`;
      validation.warnings.forEach(warning => {
        report += `- ⚠️ ${warning}\n`;
      });
      report += '\n';
    }

    // Configuration Summary (sanitized)
    report += `## Configuration Summary\n`;
    report += `- API Configured: ${Boolean(config.api.apiKey)}\n`;
    report += `- Database Configured: ${Boolean(config.database.url)}\n`;
    report += `- Rate Limiting: ${config.security.rateLimitEnabled}\n`;
    report += `- CORS Origins: ${config.security.corsOrigins.length}\n`;
    report += `- Max Tokens: ${config.api.maxTokens}\n`;
    report += `- Temperature: ${config.api.temperature}\n`;

    return report;
  }

  /**
   * Log configuration status to console
   */
  public logConfigurationStatus(): void {
    const healthCheck = this.performHealthCheck();
    const validation = this.validateConfiguration();

    console.log('🔧 Configuration Status:', {
      status: healthCheck.status,
      environment: environmentManager.getEnvironment(),
      valid: validation.isValid,
      errors: validation.errors.length,
      warnings: validation.warnings.length
    });

    if (validation.errors.length > 0) {
      console.error('❌ Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
    }

    if (validation.warnings.length > 0) {
      console.warn('⚠️ Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    if (environmentManager.getMonitoringConfig().debugMode) {
      console.log('📊 Detailed Health Check:', healthCheck);
    }
  }
}

// Export singleton instance
export const configValidator = ConfigurationValidator.getInstance();

// Export utility functions
export const validateEnvironment = () => configValidator.validateConfiguration();
export const performHealthCheck = () => configValidator.performHealthCheck();
export const generateReport = () => configValidator.generateConfigurationReport();
export const logStatus = () => configValidator.logConfigurationStatus();