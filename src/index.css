@tailwind base;
@tailwind components;
@tailwind utilities;

/* Spark Design System - Professional AI Interface inspired by X AI, Grok & Z.AI
All colors MUST be HSL. Electric Blue (#1E90FF) = hsl(210, 100%, 56%)
*/

@layer base {
  :root {
    /* Deep Black Base Theme - Professional & Clean */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 3.5%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.5%;
    --popover-foreground: 0 0% 98%;

    /* Electric Blue Primary - Inspired by Grok & X AI */
    --primary: 210 100% 56%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 210 100% 70%;

    /* Sophisticated Secondary */
    --secondary: 0 0% 7%;
    --secondary-foreground: 0 0% 92%;

    --muted: 0 0% 5%;
    --muted-foreground: 0 0% 65%;

    --accent: 210 100% 56%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 15%;
    --input: 0 0% 9%;
    --ring: 210 100% 56%;

    --radius: 0.75rem;

    /* Spark-specific tokens - Enhanced */
    --electric-blue: 210 100% 56%;
    --electric-blue-glow: 210 100% 70%;
    --deep-black: 0 0% 0%;
    --pure-white: 0 0% 100%;
    
    /* Additional professional colors */
    --neural-purple: 270 100% 65%;
    --neural-pink: 330 100% 70%;
    --success-green: 142 76% 36%;
    --warning-amber: 38 92% 50%;
    
    /* Sophisticated Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210, 100%, 56%), hsl(270, 100%, 65%));
    --gradient-secondary: linear-gradient(135deg, hsl(270, 100%, 65%), hsl(330, 100%, 70%));
    --gradient-accent: linear-gradient(135deg, hsl(210, 100%, 56%), hsl(200, 100%, 50%));
    --gradient-dark: linear-gradient(180deg, hsl(0, 0%, 0%), hsl(0, 0%, 3%));
    --gradient-glass: linear-gradient(135deg, hsl(0, 0%, 10% / 0.1), hsl(0, 0%, 5% / 0.05));
    
    /* Advanced Shadows & Glows */
    --shadow-electric: 0 0 30px hsl(210, 100%, 56% / 0.4);
    --shadow-neural: 0 0 40px hsl(270, 100%, 65% / 0.3);
    --shadow-deep: 0 10px 40px hsl(0, 0%, 0% / 0.9);
    --shadow-glass: 0 8px 32px hsl(0, 0%, 0% / 0.4);
    --shadow-card: 0 4px 20px hsl(0, 0%, 0% / 0.6);
    
    /* Smooth Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-electric: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-glass: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    
    /* Neural UI Tokens */
    --glass-bg: hsl(0 0% 100% / 0.03);
    --glass-border: hsl(0 0% 100% / 0.1);
    --hover-glow: hsl(210, 100%, 56% / 0.15);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
  }
}

/* Professional Typography & Components */
@layer components {
  /* Glass morphism effect */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
  }
  
  .glass-card {
    @apply glass;
    box-shadow: var(--shadow-glass);
  }
  
  /* Neural glow effects */
  .neural-glow {
    box-shadow: var(--shadow-electric), var(--shadow-neural);
  }
  
  .electric-glow {
    box-shadow: var(--shadow-electric);
  }
  
  /* Sophisticated text gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, hsl(210, 100%, 56%), hsl(270, 100%, 65%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-secondary {
    background: linear-gradient(135deg, hsl(270, 100%, 65%), hsl(330, 100%, 70%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Enhanced button styles */
  .btn-neural {
    @apply relative overflow-hidden;
    transition: var(--transition-smooth);
  }
  
  .btn-neural::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsl(var(--electric-blue) / 0.4), transparent);
    transition: var(--transition-smooth);
  }
  
  .btn-neural:hover::before {
    left: 100%;
  }
  
  /* Interactive hover effects */
  .interactive-card {
    @apply transition-all duration-300;
  }
  
  .interactive-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), 0 0 20px var(--hover-glow);
  }
  
  /* Loading animations */
  .neural-pulse {
    animation: neural-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes neural-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  /* Sophisticated input styles */
  .input-neural {
    @apply bg-transparent border border-border/50 rounded-lg px-4 py-3 text-foreground placeholder-muted-foreground/70 transition-all duration-200 focus:outline-none focus:border-primary/50 focus:ring-1 focus:ring-primary/20;
  }
  
  /* Neural background patterns */
  .neural-bg {
    background-image:
      radial-gradient(circle at 20% 50%, hsl(var(--electric-blue) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, hsl(var(--neural-purple) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 20%, hsl(var(--neural-pink) / 0.05) 0%, transparent 50%);
  }
  
  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}