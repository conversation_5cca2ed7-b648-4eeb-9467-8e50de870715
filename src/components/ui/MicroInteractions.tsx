import { motion } from "framer-motion";
import { memo } from "react";

interface PulseAnimationProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
}

export const PulseAnimation = memo(({ children, className = "", intensity = 'medium' }: PulseAnimationProps) => {
  const intensityMap = {
    low: { scale: [1, 1.02, 1], opacity: [1, 0.8, 1] },
    medium: { scale: [1, 1.05, 1], opacity: [1, 0.7, 1] },
    high: { scale: [1, 1.1, 1], opacity: [1, 0.6, 1] }
  };

  return (
    <motion.div
      className={className}
      animate={intensityMap[intensity]}
      transition={{
        duration: intensity === 'low' ? 3 : intensity === 'medium' ? 2 : 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
});

PulseAnimation.displayName = "PulseAnimation";

interface FloatingAnimationProps {
  children: React.ReactNode;
  className?: string;
  duration?: number;
  distance?: number;
}

export const FloatingAnimation = memo(({ 
  children, 
  className = "", 
  duration = 3, 
  distance = 10 
}: FloatingAnimationProps) => {
  return (
    <motion.div
      className={className}
      animate={{
        y: [-distance, distance, -distance]
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
});

FloatingAnimation.displayName = "FloatingAnimation";

interface SlideInOnViewProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'left' | 'right' | 'up' | 'down';
  delay?: number;
}

export const SlideInOnView = memo(({ 
  children, 
  className = "", 
  direction = 'up', 
  delay = 0 
}: SlideInOnViewProps) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left': return { x: -50, opacity: 0 };
      case 'right': return { x: 50, opacity: 0 };
      case 'down': return { y: 50, opacity: 0 };
      default: return { y: -50, opacity: 0 };
    }
  };

  return (
    <motion.div
      className={className}
      initial={getInitialPosition()}
      whileInView={{ x: 0, y: 0, opacity: 1 }}
      transition={{ 
        duration: 0.6, 
        delay,
        ease: [0.4, 0, 0.2, 1] 
      }}
      viewport={{ once: true, margin: "-50px" }}
    >
      {children}
    </motion.div>
  );
});

SlideInOnView.displayName = "SlideInOnView";

interface StaggeredChildrenProps {
  children: React.ReactNode[];
  className?: string;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export const StaggeredChildren = memo(({ 
  children, 
  className = "", 
  staggerDelay = 0.1, 
  direction = 'up' 
}: StaggeredChildrenProps) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left': return { x: -30, opacity: 0 };
      case 'right': return { x: 30, opacity: 0 };
      case 'down': return { y: 30, opacity: 0 };
      default: return { y: -30, opacity: 0 };
    }
  };

  return (
    <div className={className}>
      {children.map((child, index) => (
        <motion.div
          key={index}
          initial={getInitialPosition()}
          animate={{ x: 0, y: 0, opacity: 1 }}
          transition={{ 
            delay: index * staggerDelay,
            duration: 0.5,
            ease: [0.4, 0, 0.2, 1]
          }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
});

StaggeredChildren.displayName = "StaggeredChildren";

interface HoverScaleProps {
  children: React.ReactNode;
  className?: string;
  scale?: number;
  duration?: number;
}

export const HoverScale = memo(({ 
  children, 
  className = "", 
  scale = 1.05, 
  duration = 0.2 
}: HoverScaleProps) => {
  return (
    <motion.div
      className={className}
      whileHover={{ scale }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration }}
    >
      {children}
    </motion.div>
  );
});

HoverScale.displayName = "HoverScale";

interface GlowingEffectProps {
  children: React.ReactNode;
  className?: string;
  color?: string;
  intensity?: 'low' | 'medium' | 'high';
}

export const GlowingEffect = memo(({ 
  children, 
  className = "", 
  color = "hsl(210, 100%, 56%)", 
  intensity = 'medium' 
}: GlowingEffectProps) => {
  const intensityMap = {
    low: [0, 15, 0],
    medium: [0, 25, 0],
    high: [0, 35, 0]
  };

  const [min, max, spread] = intensityMap[intensity];

  return (
    <motion.div
      className={className}
      animate={{
        boxShadow: [
          `0 0 ${min}px ${color}`,
          `0 0 ${max}px ${color}`,
          `0 0 ${min}px ${color}`
        ]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
});

GlowingEffect.displayName = "GlowingEffect";

interface TypewriterEffectProps {
  text: string;
  className?: string;
  speed?: number;
  delay?: number;
  onComplete?: () => void;
}

export const TypewriterEffect = memo(({ 
  text, 
  className = "", 
  speed = 50, 
  delay = 0,
  onComplete 
}: TypewriterEffectProps) => {
  return (
    <motion.span
      className={className}
      initial={{ width: 0 }}
      animate={{ width: "100%" }}
      transition={{
        delay,
        duration: text.length * speed / 1000,
        ease: "linear"
      }}
      onAnimationComplete={onComplete}
      style={{ overflow: "hidden", whiteSpace: "nowrap" }}
    >
      {text}
    </motion.span>
  );
});

TypewriterEffect.displayName = "TypewriterEffect";

interface CounterAnimationProps {
  value: number;
  className?: string;
  duration?: number;
  prefix?: string;
  suffix?: string;
}

export const CounterAnimation = memo(({
  value,
  className = "",
  duration = 1,
  prefix = "",
  suffix = ""
}: CounterAnimationProps) => {
  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration }}
    >
      <motion.span
        initial={{ scale: 0.5 }}
        animate={{ scale: 1 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          duration
        }}
      >
        {prefix}
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration }}
        >
          {Math.round(value)}
        </motion.span>
        {suffix}
      </motion.span>
    </motion.span>
  );
});

CounterAnimation.displayName = "CounterAnimation";