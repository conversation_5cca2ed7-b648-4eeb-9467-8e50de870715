import { useState, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Video, Play, Download, Loader2, Clock, Settings, X, ChevronDown, CheckCircle, XCircle } from "lucide-react";
import { SparkButton } from "./SparkButton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { apiService, type VideoGenerationRequest, type VideoGenerationResponse, type VideoStatusResponse } from "@/services/apiService";
import { useNeuralTriggers } from "@/hooks/useNeuralTriggers";
import { PulseAnimation, FloatingAnimation, HoverScale, GlowingEffect, StaggeredChildren } from "@/components/ui/MicroInteractions";

interface VideoGenerationProps {
  isOpen: boolean;
  onClose: () => void;
}

interface VideoJob {
  id: string;
  prompt: string;
  status: 'processing' | 'completed' | 'failed';
  progress: number;
  videoUrl?: string;
  thumbnailUrl?: string;
  createdAt: Date;
  estimatedTime?: number;
  errorMessage?: string;
}

const VideoGenerationComponent = memo(({ isOpen, onClose }: VideoGenerationProps) => {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [videoJobs, setVideoJobs] = useState<VideoJob[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Advanced settings
  const [width, setWidth] = useState([1024]);
  const [height, setHeight] = useState([576]);
  const [duration, setDuration] = useState([5]);
  const [fps, setFps] = useState([8]);
  const [steps, setSteps] = useState([50]);
  const [cfgScale, setCfgScale] = useState([7]);
  
  const { toast } = useToast();
  const { trackMessage } = useNeuralTriggers();

  // Poll video status
  const pollVideoStatus = useCallback(async (videoId: string, jobIndex: number) => {
    try {
      const response = await apiService.getVideoStatus(videoId);
      
      if (response.success && response.data) {
        setVideoJobs(prev => {
          const updated = [...prev];
          updated[jobIndex] = {
            ...updated[jobIndex],
            status: response.data!.status,
            progress: response.data!.progress,
            videoUrl: response.data!.video_url,
            thumbnailUrl: response.data!.thumbnail_url,
            estimatedTime: response.data!.estimated_time,
            errorMessage: response.data!.error_message
          };
          return updated;
        });

        // Continue polling if still processing
        if (response.data.status === 'processing') {
          setTimeout(() => pollVideoStatus(videoId, jobIndex), 2000);
        } else if (response.data.status === 'completed') {
          toast({
            title: "🎬 Video generiert!",
            description: "Ihr Video wurde erfolgreich erstellt.",
            duration: 3000,
          });
        } else if (response.data.status === 'failed') {
          toast({
            title: "❌ Video-Generation fehlgeschlagen",
            description: response.data.error_message || "Ein Fehler ist aufgetreten.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error('Error polling video status:', error);
    }
  }, [toast]);

  const handleGenerateVideo = useCallback(async () => {
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);
    
    try {
      const request: VideoGenerationRequest = {
        prompt: prompt.trim(),
        width: width[0],
        height: height[0],
        duration: duration[0],
        fps: fps[0],
        steps: steps[0],
        cfg_scale: cfgScale[0]
      };

      const response = await apiService.generateVideo(request);
      
      if (response.success && response.data) {
        const newJob: VideoJob = {
          id: response.data.video_id,
          prompt: prompt.trim(),
          status: response.data.status,
          progress: response.data.progress,
          createdAt: response.data.created_at,
          estimatedTime: response.data.estimated_time
        };

        setVideoJobs(prev => [newJob, ...prev]);
        
        // Track user behavior for neural triggers
        trackMessage(prompt, {
          webSearch: false,
          deepThinking: false,
          tools: false,
          videoGeneration: true
        });
        
        // Start polling for status updates
        setTimeout(() => pollVideoStatus(newJob.id, 0), 2000);
        
        toast({
          title: "🎬 Video-Generation gestartet",
          description: "Ihr Video wird generiert. Dies kann einige Minuten dauern.",
          duration: 3000,
        });
        
        setPrompt("");
      } else {
        // Detailliertere Fehlermeldungen basierend auf dem Fehlercode
        let errorMessage = "Ein Fehler ist aufgetreten.";
        let errorTitle = "❌ Video-Generation fehlgeschlagen";
        
        if (response.error?.code === 'COGVIDEOX_ERROR') {
          if (response.error.message?.includes('500')) {
            errorMessage = "Serverfehler: Der Z.AI-Dienst hat einen internen Fehler gemeldet. Bitte versuchen Sie es später erneut.";
            errorTitle = "❌ Serverfehler (500)";
          } else if (response.error.message?.includes('401')) {
            errorMessage = "Authentifizierungsfehler: Bitte überprüfen Sie Ihren API-Schlüssel.";
            errorTitle = "❌ Authentifizierungsfehler";
          } else if (response.error.message?.includes('429')) {
            errorMessage = "Zu viele Anfragen: Bitte warten Sie einige Minuten vor dem nächsten Versuch.";
            errorTitle = "❌ Zu viele Anfragen";
          } else {
            errorMessage = response.error.message || "Ein Fehler ist aufgetreten.";
          }
        } else if (response.error?.code === 'INVALID_PROMPT') {
          errorMessage = "Bitte geben Sie eine gültige Video-Beschreibung ein.";
          errorTitle = "❌ Ungültige Eingabe";
        }
        
        toast({
          title: errorTitle,
          description: errorMessage,
          variant: "destructive",
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('Video generation error:', error);
      toast({
        title: "❌ Unerwarteter Fehler",
        description: "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, isGenerating, width, height, duration, fps, steps, cfgScale, pollVideoStatus, toast]);

  const handleDownloadVideo = useCallback((videoUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "📥 Video heruntergeladen",
      duration: 2000,
    });
  }, [toast]);

  const formatDuration = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'processing': return 'text-blue-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  }, []);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 backdrop-blur-lg z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.95, opacity: 0, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="bg-gradient-to-br from-background to-muted/30 border border-border/50 rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden backdrop-blur-sm"
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="flex flex-row items-center justify-between pb-6 border-b border-border/20">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-primary/20 to-primary/10 rounded-lg">
                  <Video className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Video Generation mit CogVideoX-3
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Erstellen Sie beeindruckende Videos mit KI
                  </p>
                </div>
              </div>
              <SparkButton
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-9 w-9 rounded-lg hover:bg-destructive/10 hover:text-destructive transition-all"
              >
                <X className="h-4 w-4" />
              </SparkButton>
            </CardHeader>
            
            <CardContent className="space-y-8 max-h-[70vh] overflow-y-auto pt-6">
              {/* Video Generation Form */}
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="prompt" className="text-base font-semibold">Video-Beschreibung</Label>
                    <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-md">
                      {prompt.length}/500 Zeichen
                    </span>
                  </div>
                  <Textarea
                    id="prompt"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value.slice(0, 500))}
                    placeholder="Beschreiben Sie das Video, das Sie generieren möchten... (z.B. 'Ein Sonnenuntergang am Strand mit Wellen')"
                    className="mt-1.5 min-h-[120px] resize-none border-border/50 focus:border-primary/50 transition-all rounded-lg"
                    disabled={isGenerating}
                  />
                </div>

                {/* Advanced Settings Toggle */}
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-xl border border-border/30">
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4 text-primary" />
                    <Label className="text-sm font-medium">Erweiterte Einstellungen</Label>
                  </div>
                  <SparkButton
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="h-8 px-3 rounded-lg hover:bg-primary/5 transition-all"
                  >
                    {showAdvanced ? 'Ausblenden' : 'Anzeigen'}
                    <ChevronDown className={`h-3 w-3 ml-1 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
                  </SparkButton>
                </div>

                {/* Advanced Settings */}
                <AnimatePresence>
                  {showAdvanced && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6 p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Breite</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {width[0]}px
                            </span>
                          </div>
                          <Slider
                            value={width}
                            onValueChange={setWidth}
                            max={1920}
                            min={512}
                            step={64}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>512px</span>
                            <span>1920px</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Höhe</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {height[0]}px
                            </span>
                          </div>
                          <Slider
                            value={height}
                            onValueChange={setHeight}
                            max={1080}
                            min={288}
                            step={64}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>288px</span>
                            <span>1080px</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Dauer</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {duration[0]}s
                            </span>
                          </div>
                          <Slider
                            value={duration}
                            onValueChange={setDuration}
                            max={30}
                            min={2}
                            step={1}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>2s</span>
                            <span>30s</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">FPS</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {fps[0]}
                            </span>
                          </div>
                          <Slider
                            value={fps}
                            onValueChange={setFps}
                            max={24}
                            min={8}
                            step={1}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>8</span>
                            <span>24</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Schritte</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {steps[0]}
                            </span>
                          </div>
                          <Slider
                            value={steps}
                            onValueChange={setSteps}
                            max={100}
                            min={20}
                            step={5}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>20</span>
                            <span>100</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">CFG Scale</Label>
                            <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded-md">
                              {cfgScale[0]}
                            </span>
                          </div>
                          <Slider
                            value={cfgScale}
                            onValueChange={setCfgScale}
                            max={20}
                            min={1}
                            step={0.5}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>1</span>
                            <span>20</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <HoverScale scale={1.02}>
                  <SparkButton
                    onClick={handleGenerateVideo}
                    disabled={!prompt.trim() || isGenerating}
                    className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <PulseAnimation intensity="medium">
                          <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                        </PulseAnimation>
                        <span>Video wird generiert...</span>
                      </>
                    ) : (
                      <>
                        <Video className="h-5 w-5 mr-3" />
                        <span>Video generieren</span>
                      </>
                    )}
                  </SparkButton>
                </HoverScale>
              </div>

              {/* Video Jobs List */}
              {videoJobs.length > 0 && (
                <div className="space-y-6">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-bold">Video-Generationen</h3>
                    <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-md">
                      {videoJobs.length} {videoJobs.length === 1 ? 'Video' : 'Videos'}
                    </span>
                  </div>
                  <StaggeredChildren staggerDelay={0.1} direction="up" className="space-y-4">
                    {videoJobs.map((job, index) => (
                      <HoverScale scale={1.01} key={index}>
                        <Card className="p-5 border-border/30 bg-gradient-to-br from-background to-muted/20 hover:shadow-md transition-all">
                          <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <div className={`p-1.5 rounded-lg ${
                                job.status === 'processing' ? 'bg-blue-500/20' :
                                job.status === 'completed' ? 'bg-green-500/20' :
                                'bg-red-500/20'
                              }`}>
                                {job.status === 'processing' && <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />}
                                {job.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                                {job.status === 'failed' && <XCircle className="h-4 w-4 text-red-500" />}
                              </div>
                              <div>
                                <span className={`text-sm font-semibold ${
                                  job.status === 'processing' ? 'text-blue-600' :
                                  job.status === 'completed' ? 'text-green-600' :
                                  'text-red-600'
                                }`}>
                                  {job.status === 'processing' && 'Wird verarbeitet...'}
                                  {job.status === 'completed' && 'Abgeschlossen'}
                                  {job.status === 'failed' && 'Fehlgeschlagen'}
                                </span>
                                <span className="text-xs text-muted-foreground ml-2">
                                  vor {formatDuration(Math.floor((Date.now() - job.createdAt.getTime()) / 1000))}
                                </span>
                              </div>
                            </div>
                            
                            <p className="text-sm text-foreground mb-3 line-clamp-2 bg-muted/30 p-3 rounded-lg">
                              {job.prompt}
                            </p>
                            
                            {job.status === 'processing' && (
                              <div className="space-y-3">
                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                  <div className="flex items-center gap-2">
                                    <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                                    <span>Fortschritt: {job.progress}%</span>
                                  </div>
                                  {job.estimatedTime && (
                                    <span>Ca. {Math.ceil(job.estimatedTime / 60)} min übrig</span>
                                  )}
                                </div>
                                <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                                  <motion.div
                                    className="bg-gradient-to-r from-blue-500 to-blue-600 h-full rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${job.progress}%` }}
                                    transition={{ duration: 0.5 }}
                                  />
                                </div>
                              </div>
                            )}
                            
                            {job.errorMessage && (
                              <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                                <p className="text-xs text-red-600">
                                  {job.errorMessage}
                                </p>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 ml-4">
                            {job.status === 'completed' && job.videoUrl && (
                              <>
                                <SparkButton
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => window.open(job.videoUrl, '_blank')}
                                  className="h-9 px-3 rounded-lg hover:bg-primary/5 transition-all"
                                >
                                  <Play className="h-3 w-3 mr-1" />
                                  Ansehen
                                </SparkButton>
                                <SparkButton
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadVideo(job.videoUrl, `video-${job.id}.mp4`)}
                                  className="h-9 px-3 rounded-lg hover:bg-primary/5 transition-all"
                                >
                                  <Download className="h-3 w-3 mr-1" />
                                  Herunterladen
                                </SparkButton>
                              </>
                            )}
                          </div>
                        </div>
                      </Card>
                    </HoverScale>
                  ))}
                </StaggeredChildren>
              </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

VideoGenerationComponent.displayName = "VideoGenerationComponent";

export default VideoGenerationComponent;