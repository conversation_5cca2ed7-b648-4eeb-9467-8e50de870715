import { useState, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  MoreHorizontal,
  FileText,
  Star,
  Clock,
  Bookmark,
  Code,
  Lightbulb,
  MessageSquare,
  Globe,
  Brain,
  Wrench,
  Calculator,
  Search,
  Zap,
  Video,
  Play,
  Cpu,
  BarChart3
} from "lucide-react";
import { SparkButton } from "./SparkButton";
import { Card, CardContent } from "@/components/ui/card";
import { HoverScale, PulseAnimation, FloatingAnimation, StaggeredChildren } from "@/components/ui/MicroInteractions";

interface Template {
  id: string;
  title: string;
  description: string;
  prompt: string;
  category: 'coding' | 'writing' | 'analysis' | 'creative' | 'ai-enhanced' | 'video-generation' | 'glm45-advanced';
  icon: any;
  isFavorite: boolean;
}

interface QuickAccessDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (prompt: string) => void;
}

const mockTemplates: Template[] = [
  // Video Generation Features
  {
    id: "video1",
    title: "Video Generation",
    description: "Erstelle Videos mit CogVideoX-3",
    prompt: "Erstelle ein Video mit CogVideoX-3 basierend auf folgender Beschreibung: [Video-Beschreibung]. Bitte nutze die erweiterten Video-Generation-Fähigkeiten für optimale Ergebnisse.",
    category: "video-generation",
    icon: Video,
    isFavorite: true
  },
  {
    id: "video2",
    title: "Creative Video Prompt",
    description: "Kreative Video-Ideen generieren",
    prompt: "Hilf mir bei der Erstellung kreativer Video-Prompts für CogVideoX-3. Thema: [Thema]. Gib mir mehrere detaillierte Prompt-Vorschläge mit verschiedenen Stilen und Perspektiven.",
    category: "video-generation",
    icon: Play,
    isFavorite: false
  },
  
  // GLM-4.5 Advanced Features
  {
    id: "glm45-1",
    title: "GLM-4.5 Function Calling",
    description: "Erweiterte Funktionsaufrufe mit GLM-4.5",
    prompt: "Nutze GLM-4.5 Function Calling für folgende Aufgabe: [Aufgabe]. Bitte extrahiere automatisch die notwendigen Parameter und führe die entsprechenden Funktionen aus.",
    category: "glm45-advanced",
    icon: Cpu,
    isFavorite: true
  },
  {
    id: "glm45-2",
    title: "GLM-4.5 Deep Thinking",
    description: "Strukturierte Problemlösung mit GLM-4.5",
    prompt: "Analysiere dieses komplexe Problem mit GLM-4.5 Deep Thinking: [Problem]. Zeige mir deinen strukturierten Denkprozess und die schrittweise Lösung.",
    category: "glm45-advanced",
    icon: Brain,
    isFavorite: true
  },
  {
    id: "glm45-3",
    title: "GLM-4.5 Multi-Modal",
    description: "Multi-modale Verarbeitung mit GLM-4.5",
    prompt: "Verarbeite diese multi-modale Anfrage mit GLM-4.5: [Anfrage]. Nutze die erweiterten Fähigkeiten für Text, Code und Datenanalyse.",
    category: "glm45-advanced",
    icon: BarChart3,
    isFavorite: false
  },
  
  // Enhanced Z.AI Features
  {
    id: "ai1",
    title: "Real-Time Web Search",
    description: "Get current information from the web",
    prompt: "Suche im Internet nach aktuellen Informationen über: [Thema]. Bitte gib mir die neuesten Entwicklungen, News und relevante Details.",
    category: "ai-enhanced",
    icon: Search,
    isFavorite: true
  },
  {
    id: "ai2",
    title: "Deep Analysis Mode",
    description: "Complex problem solving with AI thinking",
    prompt: "Analysiere tiefgreifend und erkläre mir detailliert: [Problem/Thema]. Nutze dabei erweiterte KI-Denkprozesse für eine umfassende Betrachtung.",
    category: "ai-enhanced",
    icon: Brain,
    isFavorite: true
  },
  {
    id: "ai3",
    title: "Current Time & Date",
    description: "Get real-time information",
    prompt: "Wie spät ist es gerade und welches Datum haben wir heute? Gib mir auch relevante Zeitinformationen für meine Zeitzone.",
    category: "ai-enhanced",
    icon: Clock,
    isFavorite: false
  },
  {
    id: "ai4",
    title: "AI-Powered Calculation",
    description: "Advanced mathematical computations",
    prompt: "Berechne mir folgende Aufgabe mit KI-Tools: [Rechenaufgabe]. Erkläre auch den Lösungsweg und zeige Zwischenschritte.",
    category: "ai-enhanced",
    icon: Calculator,
    isFavorite: false
  },
  
  // Traditionelle Templates
  {
    id: "1",
    title: "Code Review",
    description: "Professionelle Code-Analyse und Feedback",
    prompt: "Bitte überprüfe diesen Code und gib detailliertes Feedback zu:\n1. Code-Qualität und Best Practices\n2. Potentielle Bugs oder Sicherheitsprobleme\n3. Performance-Optimierungen\n4. Verbesserungsvorschläge\n\n```\n[Füge deinen Code hier ein]\n```",
    category: "coding",
    icon: Code,
    isFavorite: true
  },
  {
    id: "2",
    title: "Technische Dokumentation",
    description: "Umfassende technische Dokumentation erstellen",
    prompt: "Hilf mir bei der Erstellung professioneller technischer Dokumentation für [Projekt/Feature]. Beinhalte:\n- Überblick und Zweck\n- Installations-/Setup-Anweisungen\n- Verwendungsbeispiele\n- API-Referenz\n- Fehlerbehebungsanleitung",
    category: "writing",
    icon: FileText,
    isFavorite: false
  },
  {
    id: "3",
    title: "Problemlöser",
    description: "Komplexe Probleme systematisch aufschlüsseln",
    prompt: "Ich brauche Hilfe bei der Lösung dieses Problems: [beschreibe dein Problem]\n\nBitte:\n1. Zerlege es in kleinere Komponenten\n2. Analysiere mögliche Lösungen\n3. Gib schrittweise Empfehlungen\n4. Berücksichtige potentielle Risiken und Alternativen",
    category: "analysis",
    icon: Lightbulb,
    isFavorite: false
  },
  {
    id: "4",
    title: "Kreatives Brainstorming",
    description: "Innovative Ideen und Konzepte generieren",
    prompt: "Ich brauche kreative Ideen für [Thema/Projekt]. Bitte hilf mir beim Brainstorming durch:\n1. Generierung von 10+ einzigartigen Konzepten\n2. Erkundung verschiedener Blickwinkel und Perspektiven\n3. Kombination unerwarteter Elemente\n4. Bereitstellung umsetzbarer nächster Schritte",
    category: "creative",
    icon: Star,
    isFavorite: false
  }
];

const getCategoryColor = (category: Template['category']) => {
  switch (category) {
    case 'ai-enhanced': return 'bg-electric-blue/10 text-electric-blue border-electric-blue/30';
    case 'video-generation': return 'bg-red-500/10 text-red-400 border-red-500/20';
    case 'glm45-advanced': return 'bg-orange-500/10 text-orange-400 border-orange-500/20';
    case 'coding': return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
    case 'writing': return 'bg-green-500/10 text-green-400 border-green-500/20';
    case 'analysis': return 'bg-purple-500/10 text-purple-400 border-purple-500/20';
    case 'creative': return 'bg-pink-500/10 text-pink-400 border-pink-500/20';
    default: return 'bg-muted text-muted-foreground border-border';
  }
};

export const QuickAccessDropdown = memo(({ isOpen, onClose, onSelectTemplate }: QuickAccessDropdownProps) => {
  const [templates] = useState<Template[]>(mockTemplates);

  const handleTemplateSelect = (template: Template) => {
    onSelectTemplate(template.prompt);
    onClose();
  };

  if (!isOpen) return null;

  const favorites = templates.filter(t => t.isFavorite);
  const recent = templates.slice(0, 2); // Mock recent templates

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="absolute bottom-24 left-1/2 transform -translate-x-1/2 w-96 max-h-[60vh] overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 10 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="bg-card border border-border shadow-deep">
            <CardContent className="p-0">
              {/* Favorites Section */}
              {favorites.length > 0 && (
                <div className="p-4 border-b border-border">
                  <div className="flex items-center gap-2 mb-3">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <h3 className="font-medium text-sm">Favorites</h3>
                  </div>
                  <StaggeredChildren staggerDelay={0.05} direction="up" className="space-y-2">
                    {favorites.map((template, index) => {
                      const Icon = template.icon;
                      return (
                        <HoverScale scale={1.02} key={index}>
                          <motion.button
                            className="w-full p-3 rounded-lg text-left transition-all duration-200 hover:bg-muted group"
                            onClick={() => handleTemplateSelect(template)}
                            whileHover={{ x: 2 }}
                          >
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-full ${getCategoryColor(template.category)}`}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-sm truncate">{template.title}</h4>
                              <p className="text-xs text-muted-foreground truncate">
                                {template.description}
                              </p>
                            </div>
                            <Star className="h-4 w-4 text-yellow-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </motion.button>
                      </HoverScale>
                    );
                  })}
                </StaggeredChildren>
              </div>
              )}

              {/* Recent Section */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center gap-2 mb-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <h3 className="font-medium text-sm">Recent</h3>
                </div>
                <StaggeredChildren staggerDelay={0.05} direction="up" className="space-y-2">
                  {recent.map((template, index) => {
                    const Icon = template.icon;
                    return (
                      <HoverScale scale={1.02} key={`recent-${index}`}>
                        <motion.button
                          className="w-full p-3 rounded-lg text-left transition-all duration-200 hover:bg-muted"
                          onClick={() => handleTemplateSelect(template)}
                          whileHover={{ x: 2 }}
                        >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${getCategoryColor(template.category)}`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{template.title}</h4>
                            <p className="text-xs text-muted-foreground truncate">
                              {template.description}
                            </p>
                          </div>
                        </div>
                      </motion.button>
                    </HoverScale>
                  );
                })}
              </StaggeredChildren>
              </div>

              {/* Quick Actions */}
              <div className="p-4">
                <StaggeredChildren staggerDelay={0.1} direction="up" className="space-y-2">
                  <HoverScale scale={1.03}>
                    <SparkButton
                      variant="electric-outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => {
                        console.log('Browse all templates');
                        onClose();
                      }}
                    >
                      <Bookmark className="h-4 w-4 mr-2" />
                      Browse All Templates
                    </SparkButton>
                  </HoverScale>
                  
                  <HoverScale scale={1.03}>
                    <SparkButton
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => {
                        console.log('Create custom template');
                        onClose();
                      }}
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Create Custom Template
                    </SparkButton>
                  </HoverScale>
                </StaggeredChildren>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

QuickAccessDropdown.displayName = "QuickAccessDropdown";