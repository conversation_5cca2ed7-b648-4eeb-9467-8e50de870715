import { motion } from "framer-motion";
import mimiTechLogo from "@/assets/mimi-tech-logo.png";

interface SparkLogoProps {
  size?: "sm" | "md" | "lg";
  animated?: boolean;
  className?: string;
}

const sizeVariants = {
  sm: "h-8",
  md: "h-12", 
  lg: "h-16"
};

export const SparkLogo = ({ 
  size = "md", 
  animated = true, 
  className = "" 
}: SparkLogoProps) => {
  const logoVariants = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    hover: {
      scale: 1.05,
      filter: "drop-shadow(0 0 20px hsl(var(--electric-blue) / 0.6))",
      transition: {
        duration: 0.2,
        ease: [0.4, 0, 0.6, 1]
      }
    }
  };

  const LogoComponent = animated ? motion.img : "img";

  return (
    <LogoComponent
      src={mimiTechLogo}
      alt="MiMi Tech AI - Spark"
      className={`${sizeVariants[size]} w-auto object-contain ${className}`}
      initial={animated ? { opacity: 0, scale: 0.8 } : undefined}
      animate={animated ? { opacity: 1, scale: 1 } : undefined}
      whileHover={animated ? { 
        scale: 1.05,
        filter: "drop-shadow(0 0 20px hsl(var(--electric-blue) / 0.6))"
      } : undefined}
      transition={animated ? { duration: 0.3 } : undefined}
    />
  );
};