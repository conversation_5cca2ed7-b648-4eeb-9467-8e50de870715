import { useState } from "react";
import { motion } from "framer-motion";
import { Spark<PERSON>ogo } from "./SparkLogo";
import { SparkButton } from "./SparkButton";
import { User, <PERSON>ting<PERSON>, <PERSON><PERSON>, Bell } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { NotificationCenter } from "./NotificationCenter";
import { ProfileDropdown } from "./ProfileDropdown";

interface SparkHeaderProps {
  onToggleSidebar?: () => void;
  showSidebarToggle?: boolean;
}

export const SparkHeader = ({ onToggleSidebar, showSidebarToggle = true }: SparkHeaderProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [notificationCount] = useState(3);

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
    setShowProfile(false);
  };

  const handleProfileClick = () => {
    setShowProfile(!showProfile);
    setShowNotifications(false);
  };

  return (
    <TooltipProvider delayDuration={300}>
      <motion.header 
        className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left Section */}
            <div className="flex items-center gap-4">
              {showSidebarToggle && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SparkButton
                      variant="ghost"
                      size="icon"
                      onClick={onToggleSidebar}
                      aria-label="Toggle conversation history"
                      className="md:hidden"
                    >
                      <Menu className="h-5 w-5" />
                    </SparkButton>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>Menu</p>
                  </TooltipContent>
                </Tooltip>
              )}
              
              <SparkLogo size="md" />
              <div className="hidden sm:block">
                <h1 className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                  Spark
                </h1>
                <p className="text-sm text-muted-foreground">
                  Powered by MiMi Tech AI
                </p>
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center gap-3">
              {showSidebarToggle && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SparkButton
                      variant="ghost"
                      size="icon"
                      onClick={onToggleSidebar}
                      aria-label="Toggle conversation history"
                      className="hidden md:flex"
                    >
                      <Menu className="h-5 w-5" />
                    </SparkButton>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>Conversation History</p>
                  </TooltipContent>
                </Tooltip>
              )}
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <SparkButton 
                    variant="ghost" 
                    size="icon"
                    onClick={handleNotificationClick}
                    aria-label="Notifications"
                    className="relative"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Bell className="h-4 w-4" />
                      {notificationCount > 0 && (
                        <motion.span 
                          className="absolute -top-1 -right-1 h-3 w-3 bg-primary rounded-full animate-electric-pulse text-xs flex items-center justify-center text-primary-foreground font-bold"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {notificationCount > 9 ? '9+' : notificationCount}
                        </motion.span>
                      )}
                    </motion.div>
                  </SparkButton>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Notifications {notificationCount > 0 && `(${notificationCount})`}</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <SparkButton 
                    variant="ghost" 
                    size="icon"
                    onClick={handleProfileClick}
                    aria-label="User Profile"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <User className="h-4 w-4" />
                    </motion.div>
                  </SparkButton>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Profile & Settings</p>
                </TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <SparkButton 
                    variant="ghost" 
                    size="icon"
                    aria-label="Settings"
                  >
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 90 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Settings className="h-4 w-4" />
                    </motion.div>
                  </SparkButton>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Settings</p>
                </TooltipContent>
              </Tooltip>

              {/* Get Started & Sign In Buttons */}
              <div className="hidden sm:flex items-center gap-2">
                <SparkButton 
                  variant="ai-primary" 
                  size="sm"
                  className="animate-electric-pulse"
                >
                  Get Started
                </SparkButton>
                
                <SparkButton 
                  variant="electric-outline" 
                  size="sm"
                >
                  Sign In
                </SparkButton>
              </div>

              {/* Mobile: Combined Auth Button */}
              <div className="sm:hidden">
                <SparkButton 
                  variant="ai-primary" 
                  size="sm"
                >
                  Get Started
                </SparkButton>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
        notificationCount={notificationCount}
      />

      {/* Profile Dropdown */}
      <ProfileDropdown
        isOpen={showProfile}
        onClose={() => setShowProfile(false)}
      />
    </TooltipProvider>
  );
};