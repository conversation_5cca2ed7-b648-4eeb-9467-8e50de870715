import { useState, useEffect, use<PERSON>allback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON>, Lightbulb, Zap, Target, Heart } from "lucide-react";
import { useNeuralTriggers } from "@/hooks/useNeuralTriggers";
import { SparkButton } from "./SparkButton";
import { Card, CardContent } from "@/components/ui/card";

interface NeuralTriggerCardProps {
  trigger: {
    id: string;
    type: 'suggestion' | 'feature_highlight' | 'engagement' | 'personalization';
    title: string;
    description: string;
    action?: () => void;
    priority: number;
  };
  onDismiss: (triggerId: string) => void;
  onAction: (triggerId: string) => void;
}

const NeuralTriggerCard = memo(({ trigger, onDismiss, onAction }: NeuralTriggerCardProps) => {
  const getIcon = () => {
    switch (trigger.type) {
      case 'suggestion':
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      case 'feature_highlight':
        return <Sparkles className="h-5 w-5 text-purple-500" />;
      case 'engagement':
        return <Zap className="h-5 w-5 text-blue-500" />;
      case 'personalization':
        return <Heart className="h-5 w-5 text-pink-500" />;
      default:
        return <Target className="h-5 w-5 text-primary" />;
    }
  };

  const getGradient = () => {
    switch (trigger.type) {
      case 'suggestion':
        return 'from-yellow-500/20 to-amber-500/20';
      case 'feature_highlight':
        return 'from-purple-500/20 to-pink-500/20';
      case 'engagement':
        return 'from-blue-500/20 to-cyan-500/20';
      case 'personalization':
        return 'from-pink-500/20 to-rose-500/20';
      default:
        return 'from-primary/20 to-secondary/20';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.9 }}
      transition={{ duration: 0.3, type: "spring", stiffness: 300, damping: 25 }}
      className="relative"
    >
      <Card className={`glass-card interactive-card border-0 bg-gradient-to-br ${getGradient()} p-0 overflow-hidden`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              {getIcon()}
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-foreground mb-1">
                {trigger.title}
              </h3>
              <p className="text-xs text-muted-foreground leading-relaxed">
                {trigger.description}
              </p>
              
              {trigger.action && (
                <div className="mt-3 flex gap-2">
                  <SparkButton
                    variant="ghost"
                    size="sm"
                    onClick={() => onAction(trigger.id)}
                    className="h-7 px-3 text-xs font-medium hover:bg-primary/10"
                  >
                    Jetzt ausprobieren
                  </SparkButton>
                </div>
              )}
            </div>
            
            <SparkButton
              variant="ghost"
              size="icon"
              onClick={() => onDismiss(trigger.id)}
              className="h-6 w-6 flex-shrink-0 opacity-60 hover:opacity-100 transition-opacity"
            >
              <X className="h-3 w-3" />
            </SparkButton>
          </div>
        </CardContent>
      </Card>
      
      {/* Subtle glow effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse" />
      </div>
    </motion.div>
  );
});

NeuralTriggerCard.displayName = "NeuralTriggerCard";

export const NeuralTriggers = memo(() => {
  const { activeTriggers, dismissTrigger, getPersonalizedGreeting, getPersonalizedSuggestions } = useNeuralTriggers();
  const [visibleTriggers, setVisibleTriggers] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Trigger nacheinander anzeigen
  useEffect(() => {
    if (activeTriggers.length > 0) {
      const nextTrigger = activeTriggers.find(trigger => !visibleTriggers.includes(trigger.id));
      if (nextTrigger) {
        const timer = setTimeout(() => {
          setVisibleTriggers(prev => [...prev, nextTrigger.id]);
        }, 1000 + Math.random() * 2000); // Zufällige Verzögerung zwischen 1-3 Sekunden
        
        return () => clearTimeout(timer);
      }
    }
  }, [activeTriggers, visibleTriggers]);

  const handleDismiss = useCallback((triggerId: string) => {
    setVisibleTriggers(prev => prev.filter(id => id !== triggerId));
    dismissTrigger(triggerId);
  }, [dismissTrigger]);

  const handleAction = useCallback((triggerId: string) => {
    // Hier könnte die spezifische Aktion für jeden Trigger ausgeführt werden
    console.log(`Trigger action executed: ${triggerId}`);
    handleDismiss(triggerId);
  }, [handleDismiss]);

  const visibleTriggerData = activeTriggers.filter(trigger => visibleTriggers.includes(trigger.id));
  const personalizedSuggestions = getPersonalizedSuggestions();

  if (visibleTriggerData.length === 0 && !showSuggestions) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 max-w-sm space-y-3">
      <AnimatePresence mode="popLayout">
        {/* Personalisierte Begrüßung */}
        {visibleTriggerData.length === 0 && showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="glass-card p-4 rounded-lg"
          >
            <div className="flex items-center gap-3 mb-3">
              <div className="text-2xl">{getPersonalizedGreeting().split(' ')[1]}</div>
              <div>
                <h3 className="text-sm font-semibold">{getPersonalizedGreeting()}</h3>
                <p className="text-xs text-muted-foreground">Schön, dass Sie wieder da sind!</p>
              </div>
            </div>
            
            {personalizedSuggestions.length > 0 && (
              <div className="space-y-2">
                <p className="text-xs font-medium text-muted-foreground">Vielleicht interessiert Sie auch:</p>
                {personalizedSuggestions.map((suggestion, index) => (
                  <div key={index} className="text-xs text-foreground/80 bg-primary/5 p-2 rounded">
                    {suggestion}
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}

        {/* Aktive Trigger */}
        {visibleTriggerData.map((trigger) => (
          <NeuralTriggerCard
            key={trigger.id}
            trigger={trigger}
            onDismiss={handleDismiss}
            onAction={handleAction}
          />
        ))}
      </AnimatePresence>
      
      {/* Toggle Button für Vorschläge */}
      {visibleTriggerData.length === 0 && (
        <SparkButton
          variant="ghost"
          size="sm"
          onClick={() => setShowSuggestions(!showSuggestions)}
          className="glass-card h-8 px-3 text-xs"
        >
          {showSuggestions ? 'Ausblenden' : 'Tipps anzeigen'}
        </SparkButton>
      )}
    </div>
  );
});

NeuralTriggers.displayName = "NeuralTriggers";