import { useState, useCallback, useMemo, memo, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Send, Mic, Paperclip, Zap, Copy, RotateCcw, ThumbsUp, ThumbsDown, MoreHorizontal, AlertCircle, Video, Sparkles, Brain, Target } from "lucide-react";
import ReactMarkdown from 'react-markdown';
import { SparkButton } from "./SparkButton";
import { QuickAccessDropdown } from "./QuickAccessDropdown";
import VideoGenerationComponent from "./VideoGenerationComponent";
import { NeuralTriggers } from "./NeuralTriggers";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiService, type ChatMessage, type EnhancedGLMRequest } from "@/services/apiService";
import { apiConfig } from "@/config/api";
import { securityManager } from "@/config/security";
import { useNeuralTriggers } from "@/hooks/useNeuralTriggers";
import { useAdaptiveUI } from "@/hooks/useAdaptiveUI";
import { PulseAnimation, FloatingAnimation, HoverScale, GlowingEffect, StaggeredChildren } from "@/components/ui/MicroInteractions";

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isMarkdown?: boolean;
  error?: boolean;
  isStreaming?: boolean;
}

interface SparkTextareaProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  disabled?: boolean;
}

const SparkTextarea = memo(({ value, onChange, onSend, disabled }: SparkTextareaProps) => {
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  }, [onSend]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    // Auto-resize
    e.target.style.height = 'auto';
    e.target.style.height = Math.min(e.target.scrollHeight, 200) + 'px';
  }, [onChange]);

  return (
    <div className="flex-1 relative">
      <Textarea
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder="Fragen Sie Spark AI alles... (Unterstützt Markdown)"
        className="min-h-[60px] max-h-[200px] resize-none border-border focus:border-primary transition-all duration-200 pr-24 text-base"
        rows={1}
        disabled={disabled}
        aria-label="Chat message input"
      />
      <div className="absolute right-3 bottom-3 flex gap-1">
        <SparkButton 
          variant="ghost" 
          size="icon"
          className="h-8 w-8 hover:bg-muted"
          aria-label="Attach file"
          tabIndex={0}
        >
          <Paperclip className="h-4 w-4" />
        </SparkButton>
        <SparkButton 
          variant="ghost" 
          size="icon"
          className="h-8 w-8 hover:bg-muted"
          aria-label="Voice input"
          tabIndex={0}
        >
          <Mic className="h-4 w-4" />
        </SparkButton>
      </div>
    </div>
  );
});

SparkTextarea.displayName = "SparkTextarea";

interface MessageBubbleProps {
  message: Message;
  onCopy?: (content: string) => void;
  onRegenerate?: (messageId: string) => void;
  onFeedback?: (messageId: string, positive: boolean) => void;
}

const MessageBubble = memo(({ message, onCopy, onRegenerate, onFeedback }: MessageBubbleProps) => {
  const { toast } = useToast();

  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(message.content);
    onCopy?.(message.content);
    toast({
      title: "Copied to clipboard",
      duration: 2000,
    });
  }, [message.content, onCopy, toast]);

  const markdownComponents = useMemo(() => ({
    code({ node, inline, className, children, ...props }: any) {
      return (
        <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono" {...props}>
          {children}
        </code>
      );
    },
  }), []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.3,
        ease: "easeOut"
      }}
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <Card className={`max-w-[85%] group relative ${
        message.type === 'user'
          ? 'bg-primary text-primary-foreground shadow-electric'
          : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-primary/50 transition-colors shadow-lg'
      }`}>
        <CardContent className="p-5">
          {message.isMarkdown && message.type === 'ai' ? (
            <div className="prose prose-sm dark:prose-invert max-w-none text-gray-800 dark:text-gray-100">
              <ReactMarkdown components={markdownComponents}>
                {message.content}
              </ReactMarkdown>
            </div>
          ) : (
            <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-100">
              {message.content}
            </p>
          )}
          
          <div className="flex items-center justify-between mt-3">
            <span className="text-xs opacity-70">
              {message.timestamp.toLocaleTimeString()}
            </span>
            
            {/* Action Buttons */}
            <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <SparkButton
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={handleCopy}
                aria-label="Copy message"
              >
                <Copy className="h-3 w-3" />
              </SparkButton>
              
              {message.type === 'ai' && (
                <>
                  <SparkButton
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => onRegenerate?.(message.id)}
                    aria-label="Regenerate response"
                  >
                    <RotateCcw className="h-3 w-3" />
                  </SparkButton>
                  
                  <SparkButton
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-green-500"
                    onClick={() => onFeedback?.(message.id, true)}
                    aria-label="Good response"
                  >
                    <ThumbsUp className="h-3 w-3" />
                  </SparkButton>
                  
                  <SparkButton
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-red-500"
                    onClick={() => onFeedback?.(message.id, false)}
                    aria-label="Bad response"
                  >
                    <ThumbsDown className="h-3 w-3" />
                  </SparkButton>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
});

MessageBubble.displayName = "MessageBubble";

interface ThinkingStep {
  id: string;
  text: string;
  icon: string;
  completed: boolean;
}

interface GrokThinkingIndicatorProps {
  steps?: ThinkingStep[];
}

const GrokThinkingIndicator = memo(({ steps = [] }: GrokThinkingIndicatorProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  const defaultSteps: ThinkingStep[] = [
    { id: 'analyzing', text: 'Analysiere Ihre Anfrage...', icon: '🔍', completed: false },
    { id: 'searching', text: 'Suche nach Informationen...', icon: '🌐', completed: false },
    { id: 'processing', text: 'Verarbeite Ergebnisse...', icon: '⚡', completed: false },
    { id: 'generating', text: 'Erstelle Antwort...', icon: '✨', completed: false }
  ];

  const thinkingSteps = steps.length > 0 ? steps : defaultSteps;

  useEffect(() => {
    if (currentStep < thinkingSteps.length) {
      const timer = setTimeout(() => {
        setCompletedSteps(prev => [...prev, thinkingSteps[currentStep].id]);
        setCurrentStep(prev => prev + 1);
      }, 1200 + Math.random() * 800); // Variable timing for realism

      return () => clearTimeout(timer);
    }
  }, [currentStep, thinkingSteps]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex justify-start"
    >
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg">
        <CardContent className="p-4 space-y-3">
          <div className="flex items-center space-x-2 mb-3">
            <motion.div
              className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <span className="text-white text-xs">🧠</span>
            </motion.div>
            <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
              Spark AI is thinking...
            </span>
          </div>
          
          <div className="space-y-2">
            {thinkingSteps.map((step, index) => (
              <motion.div
                key={step.id}
                className={`flex items-center space-x-3 transition-all duration-300 ${
                  index <= currentStep ? 'opacity-100' : 'opacity-30'
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{
                  opacity: index <= currentStep ? 1 : 0.3,
                  x: 0
                }}
                transition={{ delay: index * 0.2 }}
              >
                <div className="flex items-center justify-center w-6 h-6">
                  {completedSteps.includes(step.id) ? (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                    >
                      <span className="text-white text-xs">✓</span>
                    </motion.div>
                  ) : index === currentStep ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="text-lg"
                    >
                      {step.icon}
                    </motion.div>
                  ) : (
                    <span className="text-gray-400 text-lg">{step.icon}</span>
                  )}
                </div>
                <span className={`text-sm ${
                  completedSteps.includes(step.id)
                    ? 'text-green-600 dark:text-green-400'
                    : index === currentStep
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {step.text}
                </span>
              </motion.div>
            ))}
          </div>

          {/* Progress bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-3">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${(currentStep / thinkingSteps.length) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
});

const TypingIndicator = memo(() => <GrokThinkingIndicator />);

TypingIndicator.displayName = "TypingIndicator";

export const EnhancedSparkChatInterface = memo(() => {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showQuickAccess, setShowQuickAccess] = useState(false);
  const [showVideoGeneration, setShowVideoGeneration] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');
  
  // Enhanced Z.AI Features
  const [webSearchEnabled, setWebSearchEnabled] = useState(true);
  const [deepThinkingEnabled, setDeepThinkingEnabled] = useState(true);
  const [toolsEnabled, setToolsEnabled] = useState(true);
  const [streamingEnabled, setStreamingEnabled] = useState(false);
  const [glm45ModeEnabled, setGlm45ModeEnabled] = useState(true);
  
  // Security: Generate unique client ID for rate limiting
  const [clientId] = useState(() => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 15);
    return `client_${timestamp}_${random}`;
  });
  
  const { toast } = useToast();
  const { trackMessage, getPersonalizedGreeting } = useNeuralTriggers();
  const { uiConfig, trackTyping, trackInteraction, trackFeatureUsage } = useAdaptiveUI();
  const lastInteractionRef = useRef(Date.now());

  // Check API connection on mount
  const checkConnection = useCallback(async () => {
    setConnectionStatus('checking');
    const result = await apiService.testConnection();
    setConnectionStatus(result.connected ? 'connected' : 'disconnected');
    
    if (!result.connected && result.error) {
      console.warn('API Connection Issue:', result.error);
    }
  }, []);

  // Check connection on mount
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  const handleSend = useCallback(async () => {
    if (!input.trim() || isTyping) return;

    // Security: Validate and sanitize input
    try {
      const sanitizedInput = securityManager.sanitizeInput(input.trim());
      if (sanitizedInput !== input.trim()) {
        toast({
          title: "Input Modified",
          description: "Your input was modified for security reasons.",
          variant: "default",
        });
      }
    } catch (error) {
      toast({
        title: "Input Validation Error",
        description: error instanceof Error ? error.message : "Invalid input detected.",
        variant: "destructive",
      });
      return;
    }

    // Security: Check rate limiting
    const rateLimitCheck = securityManager.checkRateLimit(clientId);
    if (!rateLimitCheck.allowed) {
      const resetTime = rateLimitCheck.resetTime ? new Date(rateLimitCheck.resetTime).toLocaleTimeString() : 'soon';
      toast({
        title: "Rate Limit Exceeded",
        description: `Please wait until ${resetTime} before sending another message.`,
        variant: "destructive",
      });
      return;
    }

    // Track user interaction for adaptive UI
    trackInteraction();
    trackFeatureUsage('send_message');
    
    // Track typing speed
    const now = Date.now();
    const timeSinceLastInteraction = now - lastInteractionRef.current;
    if (timeSinceLastInteraction < 10000) { // Within 10 seconds
      trackTyping(input.length, timeSinceLastInteraction);
    }
    lastInteractionRef.current = now;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const inputValue = input;
    setInput("");
    setIsTyping(true);

    try {
      // Convert messages to API format
      const apiMessages: ChatMessage[] = [
        ...messages.filter(msg => !msg.error).map(msg => ({
          role: msg.type === 'user' ? 'user' as const : 'assistant' as const,
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: inputValue
        }
      ];

      // Determine if we need enhanced features based on the query
      const needsWebSearch = containsCurrentInfoQuery(inputValue);
      const needsDeepThinking = containsComplexQuery(inputValue);
      const needsTools = containsCalculationOrTime(inputValue);

      // Use enhanced GLM-4.5 mode if enabled
      let response;
      if (glm45ModeEnabled) {
        const glmRequest: EnhancedGLMRequest = {
          messages: apiMessages,
          stream: streamingEnabled,
          temperature: 0.7,
          max_tokens: 4000,
          top_p: 0.8,
          thinking: deepThinkingEnabled && needsDeepThinking ? {
            type: 'enabled',
            budget_tokens: 2000
          } : {
            type: 'disabled'
          },
          tools: [
            ...(webSearchEnabled && needsWebSearch ? [{
              type: 'web_search',
              web_search: {
                enable: true
              }
            }] : []),
            ...(toolsEnabled && needsTools ? [
              {
                type: 'function',
                function: {
                  name: 'get_current_time',
                  description: 'Get the current date and time',
                  parameters: {
                    type: 'object',
                    properties: {
                      timezone: {
                        type: 'string',
                        description: 'Timezone for the time (default: Europe/Berlin)'
                      }
                    }
                  }
                }
              },
              {
                type: 'function',
                function: {
                  name: 'calculate',
                  description: 'Perform mathematical calculations',
                  parameters: {
                    type: 'object',
                    properties: {
                      expression: {
                        type: 'string',
                        description: 'Mathematical expression to calculate'
                      }
                    },
                    required: ['expression']
                  }
                }
              },
              {
                type: 'function',
                function: {
                  name: 'generate_video',
                  description: 'Generate video using CogVideoX-3',
                  parameters: {
                    type: 'object',
                    properties: {
                      prompt: {
                        type: 'string',
                        description: 'Video description prompt'
                      },
                      width: {
                        type: 'number',
                        description: 'Video width in pixels'
                      },
                      height: {
                        type: 'number',
                        description: 'Video height in pixels'
                      },
                      duration: {
                        type: 'number',
                        description: 'Video duration in seconds'
                      }
                    },
                    required: ['prompt']
                  }
                }
              }
            ] : [])
          ],
          tool_choice: 'auto'
        };

        response = await apiService.sendEnhancedGLMRequest(glmRequest);
      } else {
        // Use standard API call
        response = await apiService.sendMessage(apiMessages, {
          useWebSearch: webSearchEnabled && needsWebSearch,
          enableDeepThinking: deepThinkingEnabled && needsDeepThinking,
          useTools: toolsEnabled && needsTools,
          stream: streamingEnabled,
          clientId: clientId,
        });
      }

      if (response.success && response.data) {
        const aiMessage: Message = {
          id: response.data.id,
          type: 'ai',
          content: response.data.message,
          timestamp: response.data.timestamp,
          isMarkdown: true
        };
        
        setMessages(prev => [...prev, aiMessage]);
        
        // Track user behavior for neural triggers
        trackMessage(inputValue, {
          webSearch: needsWebSearch,
          deepThinking: needsDeepThinking,
          tools: needsTools,
          videoGeneration: false
        });
        
        // Show success notification for enhanced features
        if (needsWebSearch || needsDeepThinking || needsTools) {
          toast({
            title: "✨ Enhanced AI Response",
            description: `Used: ${[
              needsWebSearch ? 'Web Search' : '',
              needsDeepThinking ? 'Deep Thinking' : '',
              needsTools ? 'AI Tools' : ''
            ].filter(Boolean).join(', ')}`,
            duration: 3000,
          });
        }
      } else {
        // Handle API error
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: `❌ **Error**: ${response.error?.message || 'Failed to get response'}\n\nPlease check your API configuration or try again later.`,
          timestamp: new Date(),
          isMarkdown: true,
          error: true
        };
        
        setMessages(prev => [...prev, errorMessage]);
        
        toast({
          title: "API Error",
          description: response.error?.message || 'Failed to get response',
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Send message error:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `❌ **Connection Error**: Unable to reach the API service.\n\nPlease check your internet connection and API configuration.`,
        timestamp: new Date(),
        isMarkdown: true,
        error: true
      };
      
      setMessages(prev => [...prev, errorMessage]);
      
      toast({
        title: "Connection Error",
        description: "Unable to reach the API service",
        variant: "destructive",
      });
    } finally {
      setIsTyping(false);
    }
  }, [input, isTyping, messages, toast, webSearchEnabled, deepThinkingEnabled, toolsEnabled, streamingEnabled, glm45ModeEnabled]);

  // Smart feature detection functions
  const containsCurrentInfoQuery = useCallback((query: string): boolean => {
    const currentInfoKeywords = [
      'heute', 'aktuell', 'heute ist', 'welches datum', 'wieviel uhr', 'zeit',
      'neueste', 'aktuelle nachrichten', 'news', 'wetter', 'börsenkurse',
      'heute in', '2024', '2025', 'aktueller', 'neuester', 'letzte'
    ];
    return currentInfoKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    );
  }, []);

  const containsComplexQuery = useCallback((query: string): boolean => {
    const complexKeywords = [
      'erkläre mir', 'analysiere', 'vergleiche', 'bewerte', 'strategie',
      'lösung für', 'konzept', 'plan', 'architektur', 'design',
      'optimierung', 'algorithmus', 'problemlösung', 'komplex'
    ];
    return complexKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    ) || query.length > 100;
  }, []);

  const containsCalculationOrTime = useCallback((query: string): boolean => {
    const toolKeywords = [
      'berechne', 'rechne', 'wieviel', 'wie viel', 'prozent', 'summe',
      'uhrzeit', 'datum', 'zeit', 'kalender', 'tage', 'wochen',
      'mathematik', 'formel', '+', '-', '*', '/', '=', 'ergebnis'
    ];
    return toolKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    ) || /\d+\s*[\+\-\*\/]\s*\d+/.test(query);
  }, []);

  const handleRegenerate = useCallback((messageId: string) => {
    setIsTyping(true);
    toast({
      title: "Regenerating response...",
      duration: 2000,
    });
    
    setTimeout(() => {
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: "Here's a regenerated response with new insights and perspective on your question.", timestamp: new Date() }
          : msg
      ));
      setIsTyping(false);
    }, 1500);
  }, [toast]);

  const handleFeedback = useCallback((messageId: string, positive: boolean) => {
    toast({
      title: positive ? "Thanks for the positive feedback!" : "Thanks for the feedback, I'll improve!",
      duration: 2000,
    });
  }, [toast]);

  const suggestionButtons = useMemo(() => [
    "Erkläre mir KI-Technologien verständlich",
    "Hilf mir bei der Lösung eines Problems",
    "Analysiere aktuelle Entwicklungen",
    "Schreibe Code für mein Projekt"
  ], []);

  const handleQuickAccessSelect = useCallback((prompt: string) => {
    setInput(prompt);
    setShowQuickAccess(false);
    trackInteraction();
    trackFeatureUsage('quick_access_template');
  }, [trackInteraction, trackFeatureUsage]);

  // Chat Management Functions
  const clearAllChats = useCallback(() => {
    setMessages([]);
    toast({
      title: "🗑️ Chat gelöscht",
      description: "Alle Nachrichten wurden erfolgreich entfernt",
      duration: 2000,
    });
  }, [toast]);

  const deleteMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
    toast({
      title: "Nachricht gelöscht",
      duration: 1500,
    });
  }, [toast]);

  return (
    <div className="flex flex-col h-full max-w-5xl mx-auto">
      {/* Chat Header with Controls */}
      {messages.length > 0 && (
        <div className="flex justify-between items-center p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">Chat mit Spark AI</h2>
          <SparkButton
            variant="ghost"
            size="sm"
            onClick={clearAllChats}
            className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
          >
            🗑️ Chat löschen
          </SparkButton>
        </div>
      )}

      {/* Enhanced Chat Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6" role="main" aria-label="Chat conversation">
        <AnimatePresence mode="popLayout">
          {messages.length === 0 ? (
            <motion.div
              className="flex flex-col items-center justify-center h-full text-center neural-bg"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: uiConfig.animationsEnabled ?
                  (uiConfig.animationSpeed === 'fast' ? 0.3 :
                   uiConfig.animationSpeed === 'slow' ? 0.9 : 0.6) : 0,
                ease: [0.4, 0, 0.2, 1]
              }}
            >
              <div className="mb-12">
                <GlowingEffect intensity="medium" color="hsl(210, 100%, 56%)">
                  <FloatingAnimation duration={4} distance={8}>
                    <div
                      className="w-32 h-32 mx-auto mb-8 rounded-full flex items-center justify-center"
                      style={{
                        background: "linear-gradient(135deg, hsla(210, 67%, 49%, 1.00), hsl(270, 100%, 65%))"
                      }}
                    >
                      <div className="relative">
                        <PulseAnimation intensity="low">
                          <Zap className="w-16 h-16 text-white" />
                        </PulseAnimation>
                        <FloatingAnimation duration={2} distance={3}>
                          <div className="absolute -top-1 -right-1">
                            <Brain className="w-6 h-6" style={{ color: "hsla(270, 85%, 60%, 1.00)" }} />
                          </div>
                        </FloatingAnimation>
                      </div>
                    </div>
                  </FloatingAnimation>
                </GlowingEffect>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <h2 className="text-5xl font-bold text-gradient-primary mb-4">
                    {getPersonalizedGreeting()}
                  </h2>
                  <h3 className="text-3xl font-bold text-foreground mb-6">
                    Willkommen bei Spark AI
                  </h3>
                  <p className="text-xl text-muted-foreground max-w-2xl leading-relaxed">
                    Ihr fortschrittlicher KI-Assistent mit
                    <span className="text-gradient-primary font-semibold mx-1">neuralen Triggern</span>,
                    <span className="text-gradient-secondary font-semibold mx-1">adaptivem UI</span> und
                    <span className="text-gradient-primary font-semibold mx-1">intelligenten AI-Tools</span>.
                  </p>
                </motion.div>
              </div>
              
              <StaggeredChildren staggerDelay={0.1} direction="up" className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-3xl">
                {suggestionButtons.map((suggestion, index) => (
                  <HoverScale scale={1.03} key={index}>
                    <motion.button
                      className="p-6 text-left border border-border rounded-xl hover:border-primary hover:shadow-electric transition-all duration-300 group bg-card/50 backdrop-blur-sm"
                      onClick={() => setInput(suggestion)}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                        {suggestion}
                      </span>
                    </motion.button>
                  </HoverScale>
                ))}
                
                {/* Quick Access More Button */}
                <HoverScale scale={1.03}>
                  <motion.button
                    className="p-6 text-left border border-primary/40 rounded-xl hover:border-primary hover:shadow-electric transition-all duration-300 group bg-primary/5 backdrop-blur-sm"
                    onClick={() => setShowQuickAccess(true)}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center gap-3">
                      <MoreHorizontal className="h-5 w-5 text-primary" />
                      <div>
                        <span className="text-sm font-medium text-primary block">
                          Mehr Vorlagen
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Zugriff auf gespeicherte Vorlagen und Favoriten
                        </span>
                      </div>
                    </div>
                  </motion.button>
                </HoverScale>
              </StaggeredChildren>
            </motion.div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="group relative">
                <MessageBubble
                  message={message}
                  onRegenerate={handleRegenerate}
                  onFeedback={handleFeedback}
                />
                {/* Delete Message Button */}
                <motion.button
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full bg-red-500/10 hover:bg-red-500/20 text-red-500"
                  onClick={() => deleteMessage(message.id)}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.1 }}
                  aria-label="Nachricht löschen"
                >
                  <span className="text-xs">🗑️</span>
                </motion.button>
              </div>
            ))
          )}
        </AnimatePresence>

        {/* Enhanced Typing Indicator */}
        <AnimatePresence>
          {isTyping && <TypingIndicator />}
        </AnimatePresence>
      </div>

      {/* Enhanced Input Area */}
      <motion.div 
        className="border-t border-border bg-card/50 backdrop-blur-md p-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {/* API Connection Status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'disconnected' ? 'bg-red-500' :
              'bg-yellow-500 animate-pulse'
            }`} />
            <span className="text-muted-foreground">
              {connectionStatus === 'connected' ? 'API Connected' :
               connectionStatus === 'disconnected' ? 'Using Mock Responses' :
               'Checking API...'}
            </span>
            {connectionStatus === 'disconnected' && (
              <SparkButton
                variant="ghost"
                size="sm"
                onClick={checkConnection}
                className="h-6 px-2 text-xs"
              >
                Retry
              </SparkButton>
            )}
          </div>
          {!apiConfig.isConfigured() && (
            <div className="flex items-center gap-1 text-xs text-yellow-600">
              <AlertCircle className="h-3 w-3" />
              <span>Configure API key in .env</span>
            </div>
          )}
        </div>

        {/* Enhanced Z.AI Features Panel */}
        <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <Zap className="h-4 w-4 text-electric-blue" />
              Z.AI Enhanced Features
            </h3>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Auto-detected based on your query
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            <label className="flex items-center gap-2 cursor-pointer group">
              <input
                type="checkbox"
                checked={webSearchEnabled}
                onChange={(e) => setWebSearchEnabled(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-blue-600 transition-colors flex items-center gap-1">
                🌐 <span className="hidden sm:inline">Web Search</span>
              </span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer group">
              <input
                type="checkbox"
                checked={deepThinkingEnabled}
                onChange={(e) => setDeepThinkingEnabled(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-purple-600 transition-colors flex items-center gap-1">
                🧠 <span className="hidden sm:inline">Deep Thinking</span>
              </span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer group">
              <input
                type="checkbox"
                checked={toolsEnabled}
                onChange={(e) => setToolsEnabled(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-green-600 transition-colors flex items-center gap-1">
                🛠️ <span className="hidden sm:inline">AI Tools</span>
              </span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer group">
              <input
                type="checkbox"
                checked={glm45ModeEnabled}
                onChange={(e) => setGlm45ModeEnabled(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-orange-600 transition-colors flex items-center gap-1">
                ⚡ <span className="hidden sm:inline">GLM-4.5</span>
              </span>
            </label>
            <SparkButton
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowVideoGeneration(true);
                trackInteraction();
                trackFeatureUsage('video_generation');
              }}
              className="h-8 px-2 text-sm text-gray-700 dark:text-gray-300 hover:text-red-600 transition-colors flex items-center gap-1"
            >
              🎬 <span className="hidden sm:inline">Video Gen</span>
            </SparkButton>
          </div>
        </div>

        <div className="flex items-end gap-4">
          <SparkTextarea
            value={input}
            onChange={setInput}
            onSend={handleSend}
            disabled={isTyping}
          />
          
          <SparkButton
            onClick={handleSend}
            disabled={!input.trim() || isTyping}
            variant="ai-primary"
            size="icon"
            className="h-[60px] w-[60px] flex-shrink-0"
            aria-label="Send message"
          >
            <Send className="h-6 w-6" />
          </SparkButton>
        </div>
        
        <div className="flex justify-between items-center mt-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-4 flex-wrap">
            <span>⌨️ <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded">Enter</kbd> to send</span>
            <span>📝 <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded">Shift+Enter</kbd> for new line</span>
            <span>✨ Enhanced with Z.AI API</span>
            <span className="hidden md:inline">🚀 Supports Web Search, Deep Thinking & AI Tools</span>
          </div>
          <span className={`${input.length > 3500 ? 'text-destructive' : ''}`}>
            {input.length}/4000
          </span>
        </div>
      </motion.div>

      {/* Quick Access Dropdown */}
      <QuickAccessDropdown
        isOpen={showQuickAccess}
        onClose={() => setShowQuickAccess(false)}
        onSelectTemplate={handleQuickAccessSelect}
      />

      {/* Video Generation Component */}
      <VideoGenerationComponent
        isOpen={showVideoGeneration}
        onClose={() => setShowVideoGeneration(false)}
      />
      
      {/* Neural Triggers Component - only show if enabled in adaptive UI */}
      {uiConfig.neuralTriggers && <NeuralTriggers />}
    </div>
  );
});

EnhancedSparkChatInterface.displayName = "EnhancedSparkChatInterface";