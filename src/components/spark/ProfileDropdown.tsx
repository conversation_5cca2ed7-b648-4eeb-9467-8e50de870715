import { useState, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  Settings, 
  Shield, 
  Download, 
  LogOut, 
  Camera,
  Globe,
  Key,
  FileText,
  ChevronRight
} from "lucide-react";
import { SparkButton } from "./SparkButton";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

interface ProfileDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  userName?: string;
  userEmail?: string;
  userAvatar?: string;
}

interface MenuItem {
  id: string;
  label: string;
  description?: string;
  icon: any;
  action: () => void;
  variant?: 'default' | 'destructive';
}

export const ProfileDropdown = memo(({ 
  isOpen, 
  onClose, 
  userName = "John Doe",
  userEmail = "<EMAIL>",
  userAvatar 
}: ProfileDropdownProps) => {
  const [activeSection, setActiveSection] = useState<string>('main');

  const mainMenuItems: MenuItem[] = [
    {
      id: "profile",
      label: "Your Spark Profile",
      description: "Manage avatar, name, and preferences",
      icon: User,
      action: () => setActiveSection('profile')
    },
    {
      id: "security", 
      label: "Security & Privacy",
      description: "Password, 2FA, and SSO settings",
      icon: Shield,
      action: () => setActiveSection('security')
    },
    {
      id: "data",
      label: "Data & Export",
      description: "Download conversations and manage data",
      icon: Download,
      action: () => setActiveSection('data')
    }
  ];

  const profileMenuItems: MenuItem[] = [
    {
      id: "avatar",
      label: "Upload Avatar",
      icon: Camera,
      action: () => console.log('Upload avatar')
    },
    {
      id: "language",
      label: "Language Settings",
      icon: Globe,
      action: () => console.log('Language settings')
    },
    {
      id: "preferences",
      label: "Chat Preferences",
      icon: Settings,
      action: () => console.log('Chat preferences')
    }
  ];

  const securityMenuItems: MenuItem[] = [
    {
      id: "password",
      label: "Change Password",
      icon: Key,
      action: () => console.log('Change password')
    },
    {
      id: "2fa",
      label: "Two-Factor Auth",
      icon: Shield,
      action: () => console.log('2FA settings')
    },
    {
      id: "sessions",
      label: "Active Sessions",
      icon: Settings,
      action: () => console.log('Active sessions')
    }
  ];

  const dataMenuItems: MenuItem[] = [
    {
      id: "export",
      label: "Export Conversations",
      icon: Download,
      action: () => console.log('Export data')
    },
    {
      id: "privacy",
      label: "Privacy Settings",
      icon: FileText,
      action: () => console.log('Privacy settings')
    },
    {
      id: "delete",
      label: "Delete Account",
      icon: LogOut,
      action: () => console.log('Delete account'),
      variant: 'destructive' as const
    }
  ];

  const getCurrentItems = () => {
    switch (activeSection) {
      case 'profile': return profileMenuItems;
      case 'security': return securityMenuItems;
      case 'data': return dataMenuItems;
      default: return mainMenuItems;
    }
  };

  const getCurrentTitle = () => {
    switch (activeSection) {
      case 'profile': return 'Profile Settings';
      case 'security': return 'Security & Privacy';
      case 'data': return 'Data Management';
      default: return 'Account Menu';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="absolute top-16 right-6 w-80"
          initial={{ opacity: 0, scale: 0.95, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -10 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="bg-card border border-border shadow-deep">
            <CardContent className="p-0">
              {/* Header */}
              <div className="p-4">
                {activeSection !== 'main' && (
                  <SparkButton
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveSection('main')}
                    className="mb-3 -ml-2"
                  >
                    ← Back
                  </SparkButton>
                )}
                
                {activeSection === 'main' && (
                  <div className="flex items-center gap-3 mb-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={userAvatar} />
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold truncate">{userName}</h3>
                      <p className="text-sm text-muted-foreground truncate">{userEmail}</p>
                    </div>
                  </div>
                )}
                
                <h4 className="font-medium text-sm text-muted-foreground">
                  {getCurrentTitle()}
                </h4>
              </div>

              <Separator />

              {/* Menu Items */}
              <div className="p-2">
                {getCurrentItems().map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <motion.button
                      key={item.id}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-200 hover:bg-muted group ${
                        item.variant === 'destructive' ? 'hover:bg-destructive/10' : ''
                      }`}
                      onClick={item.action}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      whileHover={{ x: 2 }}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className={`h-4 w-4 ${
                          item.variant === 'destructive' 
                            ? 'text-destructive' 
                            : 'text-muted-foreground group-hover:text-foreground'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <div className={`font-medium text-sm ${
                            item.variant === 'destructive' ? 'text-destructive' : ''
                          }`}>
                            {item.label}
                          </div>
                          {item.description && (
                            <div className="text-xs text-muted-foreground">
                              {item.description}
                            </div>
                          )}
                        </div>
                        {activeSection === 'main' && (
                          <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                        )}
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              {activeSection === 'main' && (
                <>
                  <Separator />
                  <div className="p-2">
                    <motion.button
                      className="w-full p-3 rounded-lg text-left transition-all duration-200 hover:bg-destructive/10 group"
                      onClick={() => console.log('Sign out')}
                      whileHover={{ x: 2 }}
                    >
                      <div className="flex items-center gap-3">
                        <LogOut className="h-4 w-4 text-destructive" />
                        <span className="font-medium text-sm text-destructive">
                          Sign Out
                        </span>
                      </div>
                    </motion.button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

ProfileDropdown.displayName = "ProfileDropdown";