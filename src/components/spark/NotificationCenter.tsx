import { useState, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Bell, 
  Sparkles, 
  BookOpen, 
  Zap,
  X,
  CheckCircle
} from "lucide-react";
import { SparkButton } from "./SparkButton";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { OnboardingModal } from "./OnboardingModal";

interface Notification {
  id: string;
  type: 'onboarding' | 'feature' | 'update' | 'achievement';
  title: string;
  description: string;
  timestamp: Date;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  notificationCount: number;
}

const mockNotifications: Notification[] = [
  {
    id: "1",
    type: "onboarding",
    title: "Complete Your Setup",
    description: "3 quick steps left to unlock Spark's full potential",
    timestamp: new Date(),
    read: false
  },
  {
    id: "2",
    type: "feature",
    title: "New: Code Syntax Highlighting",
    description: "Spark now beautifully formats code in 150+ languages",
    timestamp: new Date(Date.now() - 1000 * 60 * 60),
    read: false
  },
  {
    id: "3",
    type: "achievement",
    title: "First Conversation Complete! 🎉",
    description: "You've started your journey with Spark AI",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    read: true
  }
];

const getNotificationIcon = (type: Notification['type']) => {
  switch (type) {
    case 'onboarding': return CheckCircle;
    case 'feature': return Sparkles;
    case 'update': return BookOpen;
    case 'achievement': return Zap;
    default: return Bell;
  }
};

const getNotificationColor = (type: Notification['type']) => {
  switch (type) {
    case 'onboarding': return 'bg-primary/10 text-primary border-primary/20';
    case 'feature': return 'bg-purple-500/10 text-purple-400 border-purple-500/20';
    case 'update': return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
    case 'achievement': return 'bg-green-500/10 text-green-400 border-green-500/20';
    default: return 'bg-muted text-muted-foreground border-border';
  }
};

export const NotificationCenter = memo(({ isOpen, onClose, notificationCount }: NotificationCenterProps) => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [showOnboarding, setShowOnboarding] = useState(false);

  const handleNotificationClick = (notification: Notification) => {
    if (notification.type === 'onboarding') {
      setShowOnboarding(true);
    }
    
    // Mark as read
    setNotifications(prev => prev.map(n => 
      n.id === notification.id ? { ...n, read: true } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (!isOpen) return null;

  return (
    <>
      <AnimatePresence>
        <motion.div
          className="fixed inset-0 z-40"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="absolute top-16 right-6 w-96 max-h-[80vh] overflow-hidden"
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            onClick={(e) => e.stopPropagation()}
          >
            <Card className="bg-card border border-border shadow-deep">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg">Notifications</CardTitle>
                    {unreadCount > 0 && (
                      <Badge variant="secondary" className="bg-primary text-primary-foreground">
                        {unreadCount}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {unreadCount > 0 && (
                      <SparkButton
                        variant="ghost"
                        size="sm"
                        onClick={markAllAsRead}
                        className="text-xs"
                      >
                        Mark all read
                      </SparkButton>
                    )}
                    <SparkButton
                      variant="ghost"
                      size="icon"
                      onClick={onClose}
                      className="h-8 w-8"
                    >
                      <X className="h-4 w-4" />
                    </SparkButton>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-0 max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-6 text-center text-muted-foreground">
                    <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No notifications yet</p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {notifications.map((notification, index) => {
                      const Icon = getNotificationIcon(notification.type);
                      return (
                        <motion.button
                          key={notification.id}
                          className={`w-full p-4 text-left transition-all duration-200 hover:bg-muted border-l-4 ${
                            !notification.read ? 'bg-muted/50' : ''
                          } ${getNotificationColor(notification.type)}`}
                          onClick={() => handleNotificationClick(notification)}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ x: 2 }}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-full mt-1 ${getNotificationColor(notification.type)}`}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm truncate">
                                  {notification.title}
                                </h4>
                                {!notification.read && (
                                  <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                                )}
                              </div>
                              <p className="text-xs text-muted-foreground line-clamp-2">
                                {notification.description}
                              </p>
                              <span className="text-xs text-muted-foreground/70 mt-1 block">
                                {notification.timestamp.toLocaleTimeString([], { 
                                  hour: '2-digit', 
                                  minute: '2-digit' 
                                })}
                              </span>
                            </div>
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </AnimatePresence>

      <OnboardingModal
        isOpen={showOnboarding}
        onClose={() => setShowOnboarding(false)}
        onTaskComplete={(taskId) => {
          console.log(`Task completed: ${taskId}`);
          // Here you would update the onboarding progress
        }}
      />
    </>
  );
});

NotificationCenter.displayName = "NotificationCenter";