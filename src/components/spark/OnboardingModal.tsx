import { useState, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Bell, 
  CheckCircle, 
  Circle, 
  User, 
  Camera, 
  Globe, 
  Play,
  X,
  ChevronRight
} from "lucide-react";
import { SparkButton } from "./SparkButton";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface OnboardingTask {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  icon: any;
}

interface OnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskComplete: (taskId: string) => void;
}

const defaultTasks: OnboardingTask[] = [
  {
    id: "profile",
    title: "Complete Your Profile",
    description: "Add your name and basic information",
    completed: false,
    icon: User
  },
  {
    id: "avatar",
    title: "Upload Avatar",
    description: "Personalize your Spark experience",
    completed: false,
    icon: Camera
  },
  {
    id: "language",
    title: "Choose Language",
    description: "Set your preferred language for Spark",
    completed: true,
    icon: Globe
  },
  {
    id: "tour",
    title: "Take Introduction Tour",
    description: "Learn about Spark's powerful features",
    completed: false,
    icon: Play
  }
];

export const OnboardingModal = memo(({ isOpen, onClose, onTaskComplete }: OnboardingModalProps) => {
  const [tasks, setTasks] = useState<OnboardingTask[]>(defaultTasks);
  
  const completedTasks = tasks.filter(task => task.completed).length;
  const totalTasks = tasks.length;
  const progressPercentage = (completedTasks / totalTasks) * 100;

  const handleTaskClick = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, completed: !task.completed } : task
    ));
    onTaskComplete(taskId);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="w-full max-w-md"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="bg-card border border-border shadow-electric">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-bold mb-2">
                    Welcome to Spark! 🚀
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Complete your setup to unlock the full potential
                  </p>
                </div>
                <SparkButton
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </SparkButton>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">
                    {completedTasks}/{totalTasks} completed
                  </span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {tasks.map((task, index) => {
                const Icon = task.icon;
                return (
                  <motion.div
                    key={task.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                      task.completed 
                        ? 'bg-primary/10 border-primary/20' 
                        : 'bg-muted/50 border-border hover:border-primary/40'
                    }`}
                    onClick={() => handleTaskClick(task.id)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-full ${
                        task.completed ? 'bg-primary' : 'bg-muted'
                      }`}>
                        {task.completed ? (
                          <CheckCircle className="h-4 w-4 text-primary-foreground" />
                        ) : (
                          <Icon className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{task.title}</h4>
                        <p className="text-xs text-muted-foreground">{task.description}</p>
                      </div>
                      
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </motion.div>
                );
              })}

              <div className="pt-4 space-y-3">
                {progressPercentage === 100 ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center"
                  >
                    <Badge variant="secondary" className="bg-primary text-primary-foreground">
                      🎉 All set! You're ready to spark!
                    </Badge>
                  </motion.div>
                ) : (
                  <SparkButton
                    variant="electric-outline"
                    className="w-full"
                    onClick={onClose}
                  >
                    Continue Setup Later
                  </SparkButton>
                )}
                
                <SparkButton
                  variant="ai-primary"
                  className="w-full"
                  onClick={onClose}
                >
                  Start Using Spark
                </SparkButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

OnboardingModal.displayName = "OnboardingModal";