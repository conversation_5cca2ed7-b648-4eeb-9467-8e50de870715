import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

const sparkButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[colors,transform,box-shadow] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        electric: "bg-primary text-primary-foreground shadow-electric hover:shadow-[0_0_40px_hsl(var(--electric-blue)_/_0.5)] hover:scale-105",
        "electric-outline": "border border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground hover:shadow-electric",
        ghost: "hover:bg-muted hover:text-accent-foreground",
        dark: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        "ai-primary": "bg-gradient-primary text-pure-white shadow-electric hover:shadow-[0_0_50px_hsl(var(--electric-blue)_/_0.7)] hover:scale-[1.02] animate-electric-pulse",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-14 rounded-lg px-12 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "electric",
      size: "default",
    },
  }
);

export interface SparkButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof sparkButtonVariants> {
  asChild?: boolean;
  animated?: boolean;
}

const SparkButton = React.forwardRef<HTMLButtonElement, SparkButtonProps>(
  ({ className, variant, size, asChild = false, animated = true, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    
    const buttonVariants = {
      tap: { scale: 0.98 },
      hover: {
        y: -1,
        transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] }
      }
    };

    if (animated) {
      return (
        <motion.div
          whileTap={{ scale: 0.98 }}
          whileHover={{ y: -1 }}
          transition={{ duration: 0.2 }}
        >
          <Comp
            className={cn(sparkButtonVariants({ variant, size, className }))}
            ref={ref}
            {...props}
          />
        </motion.div>
      );
    }

    return (
      <Comp
        className={cn(sparkButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
SparkButton.displayName = "SparkButton";

export { SparkButton, sparkButtonVariants };