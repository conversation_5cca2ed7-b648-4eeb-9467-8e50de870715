import { useState, use<PERSON><PERSON>back, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  MessageSquare, 
  History, 
  Settings, 
  Trash2, 
  Download, 
  Search,
  Plus,
  X
} from "lucide-react";
import { SparkButton } from "./SparkButton";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
}

interface SparkSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  currentConversationId?: string;
  onSelectConversation: (id: string) => void;
  onNewConversation: () => void;
  onDeleteConversation: (id: string) => void;
}

const mockConversations: Conversation[] = [
  {
    id: "1",
    title: "Quantum Computing Explained",
    lastMessage: "Thanks for the clear explanation!",
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    messageCount: 12
  },
  {
    id: "2", 
    title: "Python Data Analysis Script",
    lastMessage: "The code works perfectly, thank you!",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    messageCount: 8
  },
  {
    id: "3",
    title: "Work Schedule Planning",
    lastMessage: "This schedule looks great for productivity.",
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
    messageCount: 15
  }
];

const ConversationItem = memo(({ 
  conversation, 
  isActive, 
  onClick, 
  onDelete 
}: {
  conversation: Conversation;
  isActive: boolean;
  onClick: () => void;
  onDelete: () => void;
}) => {
  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  }, [onDelete]);

  return (
    <motion.div
      className={`p-3 rounded-lg cursor-pointer transition-all duration-200 group relative ${
        isActive 
          ? 'bg-primary text-primary-foreground shadow-electric' 
          : 'hover:bg-muted'
      }`}
      onClick={onClick}
      whileHover={{ x: 2 }}
      layout
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm truncate mb-1">
            {conversation.title}
          </h4>
          <p className="text-xs opacity-75 truncate mb-2">
            {conversation.lastMessage}
          </p>
          <div className="flex items-center gap-2 text-xs opacity-60">
            <MessageSquare className="h-3 w-3" />
            <span>{conversation.messageCount}</span>
            <span>•</span>
            <span>{conversation.timestamp.toLocaleDateString()}</span>
          </div>
        </div>
        
        <SparkButton
          variant="ghost"
          size="icon"
          className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={handleDelete}
          aria-label="Delete conversation"
        >
          <Trash2 className="h-3 w-3" />
        </SparkButton>
      </div>
    </motion.div>
  );
});

ConversationItem.displayName = "ConversationItem";

export const SparkSidebar = memo(({ 
  isOpen, 
  onToggle, 
  currentConversationId, 
  onSelectConversation,
  onNewConversation,
  onDeleteConversation 
}: SparkSidebarProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [conversations] = useState<Conversation[]>(mockConversations);

  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.aside
          className="fixed left-0 top-0 z-40 h-full w-80 bg-card border-r border-border shadow-deep"
          initial={{ x: -320 }}
          animate={{ x: 0 }}
          exit={{ x: -320 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        >
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Conversations
                </h2>
                <SparkButton
                  variant="ghost"
                  size="icon"
                  onClick={onToggle}
                  aria-label="Close sidebar"
                >
                  <X className="h-4 w-4" />
                </SparkButton>
              </div>
              
              <div className="space-y-3">
                <SparkButton
                  onClick={onNewConversation}
                  variant="electric"
                  className="w-full justify-start"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Conversation
                </SparkButton>
                
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search conversations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Conversations List */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-2">
                <AnimatePresence>
                  {filteredConversations.map((conversation) => (
                    <ConversationItem
                      key={conversation.id}
                      conversation={conversation}
                      isActive={conversation.id === currentConversationId}
                      onClick={() => onSelectConversation(conversation.id)}
                      onDelete={() => onDeleteConversation(conversation.id)}
                    />
                  ))}
                </AnimatePresence>
                
                {filteredConversations.length === 0 && (
                  <motion.div
                    className="text-center py-8 text-muted-foreground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">
                      {searchQuery ? 'No conversations found' : 'No conversations yet'}
                    </p>
                  </motion.div>
                )}
              </div>
            </ScrollArea>

            {/* Footer */}
            <div className="p-4 border-t border-border">
              <div className="space-y-2">
                <SparkButton
                  variant="ghost"
                  className="w-full justify-start"
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Conversations
                </SparkButton>
                
                <SparkButton
                  variant="ghost"
                  className="w-full justify-start"
                  size="sm"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </SparkButton>
              </div>
            </div>
          </div>
        </motion.aside>
      )}
      
      {/* Overlay */}
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onToggle}
        />
      )}
    </AnimatePresence>
  );
});

SparkSidebar.displayName = "SparkSidebar";