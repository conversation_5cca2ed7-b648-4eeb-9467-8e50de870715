# Production Environment Configuration
# This file contains production-specific environment variables

# Environment
NODE_ENV=production
VITE_ENV=production
VERCEL_ENV=production

# Z.AI API Configuration
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_API_KEY=9dbec631df674f24a9d24149ca9ff0b7.KM2cUiTNP5pfNS0m
VITE_API_VERSION=v4
VITE_API_TIMEOUT=30000

# Model Configuration for Z.AI
VITE_DEFAULT_MODEL=glm-4-flash
VITE_MAX_TOKENS=4000
VITE_TEMPERATURE=0.7

# Video Generation Configuration
VITE_VIDEO_MODEL=cogvideox-3
VITE_VIDEO_MAX_WIDTH=1920
VITE_VIDEO_MAX_HEIGHT=1080
VITE_VIDEO_MAX_DURATION=30
VITE_VIDEO_MAX_STEPS=100

# Retry Configuration
VITE_MAX_RETRIES=3
VITE_RETRY_DELAY=2000
VITE_EXPONENTIAL_BACKOFF=true

# Debug and Monitoring (production optimized)
VITE_DEBUG_MODE=false
VITE_ANALYTICS_ENABLED=true
VITE_ERROR_TRACKING_ENABLED=true
VITE_PERFORMANCE_MONITORING=true

# Security Configuration (strict for production)
VITE_RATE_LIMIT_ENABLED=true
VITE_RATE_LIMIT_MAX_REQUESTS=60
VITE_RATE_LIMIT_WINDOW_MS=60000
VITE_MAX_INPUT_LENGTH=10000
VITE_LOG_SECURITY_EVENTS=true
VITE_CORS_ORIGINS=spark-ai-experience.vercel.app,api.z.ai,open.bigmodel.cn

# Database Configuration (Vercel Postgres production)
POSTGRES_URL=
POSTGRES_PRISMA_URL=
POSTGRES_URL_NON_POOLING=
POSTGRES_USER=
POSTGRES_HOST=
POSTGRES_PASSWORD=
POSTGRES_DATABASE=

# Production-specific features
VITE_MOCK_API_ENABLED=false
VITE_FEATURE_FLAGS_ENABLED=true
VITE_A_B_TESTING_ENABLED=false
VITE_COMPRESSION_ENABLED=true
VITE_CDN_ENABLED=true