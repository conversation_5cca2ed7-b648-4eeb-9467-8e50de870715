# Spark AI Experience - Enhanced Z.AI Integration

Eine vollständig funktionale React-basierte Chat-Anwendung mit erweiterten Z.AI API Features für intelligente KI-Antworten, Web-Suche, Deep Thinking und AI-Tools.

## 🌟 Enhanced Features

### 🧠 **Erweiterte Z.AI Integration**
- **GLM-4.5 Modell**: Neueste Z.AI Sprachtechnologie mit erweiterten Fähigkeiten
- **Echtzeit Web-Suche**: Zugriff auf aktuelle Informationen aus dem Internet
- **Deep Thinking Modus**: Komplexe Problemlösung mit fortgeschrittenen KI-Denkprozessen
- **AI Tools Integration**: Mathematische Berechnungen, Zeitabfragen und mehr
- **Function Calling**: Dynamische Tool-Aktivierung basierend auf Benutzer-Anfragen
- **Streaming Responses**: Echtzeit-Antwort-Streaming (optional)

### 🎯 **Intelligente Feature-Erkennung**
- **Auto-Erkennung**: Automatische Aktivierung relevanter Features basierend auf Anfrage-Inhalt
- **Aktuelle Info-Anfragen**: Web-Suche für News, Wetter, aktuelle Ereignisse
- **Komplexe Analysen**: Deep Thinking für detaillierte Erklärungen und Analysen
- **Berechnungen**: AI Tools für mathematische Operationen und Zeit-Anfragen

### 🎨 **Enhanced User Interface**
- **Z.AI Features Panel**: Live-Kontrolle über erweiterte Features
- **Modernes Chat-Interface**: Schönes, responsives Design mit sanften Animationen
- **Enhanced Templates**: Z.AI-spezifische Schnellzugriff-Templates
- **Verbindungsstatus**: Echtzeit API-Status-Überwachung
- **Markdown-Unterstützung**: Vollständige Markdown-Rendering für formatierte Antworten
- **Dark/Light Mode**: Automatische Theme-Erkennung

### 🚀 **Performance & Zuverlässigkeit**
- **Intelligente Fallback-Strategie**: Nahtloser Wechsel zu Mock-Antworten bei API-Fehlern
- **Erweiterte Fehlerbehandlung**: Umfassende Fehlerbehandlung mit benutzerfreundlichen Nachrichten
- **Context Caching**: Erweiterte Kontext-Verwaltung für bessere Gespräche
- **Response Optimization**: Effiziente API-Request-Behandlung

## 🎛️ Z.AI Enhanced Features Panel

Die Anwendung enthält ein Live-Kontroll-Panel für alle erweiterten Features:

- **🌐 Web Search**: Echtzeit-Internet-Suche für aktuelle Informationen
- **🧠 Deep Thinking**: Erweiterte Denkprozesse für komplexe Anfragen
- **🛠️ AI Tools**: Mathematische Berechnungen, Zeitabfragen, Coding-Unterstützung
- **📡 Streaming**: Echtzeit-Antwort-Streaming (experimentell)

Features werden automatisch erkannt und basierend auf Ihrem Anfrage-Inhalt aktiviert.

## 📋 Voraussetzungen

- Node.js (v18 oder höher)
- npm oder yarn
- Z.AI API-Konto mit gültigem API-Key und ausreichend Guthaben

## 🛠️ Installation

1. **Repository klonen:**
```bash
git clone https://github.com/MimiTechAi/spark-ai-experience.git
cd spark-ai-experience
```

2. **Dependencies installieren:**
```bash
npm install
```

3. **Umgebungsvariablen konfigurieren:**
```bash
cp .env.example .env
```

4. **Z.AI API-Konfiguration in `.env` anpassen:**
```env
# Z.AI API Configuration
VITE_API_KEY=your_zai_api_key_here
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_DEBUG_MODE=false

# Model Configuration
VITE_DEFAULT_MODEL=glm-4-flash
VITE_MAX_TOKENS=4000
VITE_TEMPERATURE=0.7
```

5. **Entwicklungsserver starten:**
```bash
npm run dev
```

Die Anwendung ist dann unter `http://localhost:5173` verfügbar.

## 🚀 Vercel Deployment

### Schnell-Deployment

```bash
# 1. Deployment vorbereiten
npm run deploy

# 2. Vercel Dashboard öffnen und Repository importieren
# 3. Umgebungsvariablen konfigurieren (siehe DEPLOYMENT.md)
# 4. Deployen!
```

**📖 Detaillierte Anleitung**: Siehe [DEPLOYMENT.md](DEPLOYMENT.md)

### Umgebungsvariablen für Vercel

Konfigurieren Sie diese Variablen in Ihren Vercel Project Settings:

```env
VITE_API_ENDPOINT=https://open.bigmodel.cn/api/paas/v4
VITE_API_KEY=[IHR_ZAI_API_KEY]
VITE_API_VERSION=v4
VITE_DEFAULT_MODEL=glm-4-flash
VITE_DEBUG_MODE=false
```

**⚠️ Wichtig**: Niemals API-Keys in den Code committen!

## 🔧 Z.AI Enhanced API Setup

### 1. API-Key erhalten

1. Besuchen Sie [Z.AI Platform](https://open.bigmodel.cn)
2. Registrieren Sie sich für ein Konto
3. Navigieren Sie zu den API-Einstellungen
4. Erstellen Sie einen neuen API-Key
5. **Wichtig**: Stellen Sie sicher, dass Ihr Konto ausreichend Guthaben hat

### 2. Enhanced API-Konfiguration

Die erweiterte API-Konfiguration erfolgt über Umgebungsvariablen:

| Variable | Beschreibung | Standard | Erforderlich |
|----------|-------------|----------|--------------|
| `VITE_ZAI_API_KEY` | Ihr Z.AI API-Schlüssel | - | ✅ |
| `VITE_ZAI_BASE_URL` | Z.AI API Basis-URL | `https://open.bigmodel.cn/api/paas/v4` | ❌ |
| `VITE_DEBUG_MODE` | Debug-Logging aktivieren | `false` | ❌ |

### 3. Enhanced API-Integration Details

Die erweiterte API-Integration verwendet folgende Struktur:

```javascript
// Enhanced API Request Format
{
  "model": "glm-4-flash",
  "messages": [
    {
      "role": "user",
      "content": "Ihre Nachricht"
    }
  ],
  "max_tokens": 4000,
  "temperature": 0.7,
  "top_p": 0.8,
  "tools": [
    {
      "type": "web_search",
      "web_search": {
        "search_result": true
      }
    },
    {
      "type": "function",
      "function": {
        "name": "get_current_time",
        "description": "Get current date and time"
      }
    }
  ],
  "tool_choice": "auto"
}
```

## 🎯 Feature Usage Beispiele

### 🌐 Web Search Anfragen
```
"Was sind die neuesten KI-Entwicklungen 2024?"
"Aktuelle Nachrichten über Künstliche Intelligenz"
"Wie ist das Wetter heute in Berlin?"
"Aktuelle Börsenkurse"
```

### 🧠 Deep Thinking Anfragen
```
"Erkläre mir detailliert die Quantencomputing-Technologie"
"Analysiere die Vor- und Nachteile von KI in der Bildung"
"Entwickle eine Strategie für nachhaltiges Wachstum"
"Bewerte die Auswirkungen der Digitalisierung"
```

### 🛠️ AI Tools Anfragen
```
"Berechne 15% von 2.847"
"Wie spät ist es gerade?"
"Welches Datum haben wir heute?"
"Rechne 125 * 47 + 233"
```

## 🔍 Enhanced API-Status-Überwachung

Die Anwendung überwacht automatisch den erweiterten API-Status:

- 🟢 **API Connected**: Z.AI API ist verfügbar und alle Features funktionieren
- 🔴 **Using Mock Responses**: API nicht verfügbar, intelligente Mock-Antworten werden verwendet
- 🟡 **Checking API**: Verbindung und Features werden getestet

## 🚨 Enhanced Fehlerbehebung

### Häufige Probleme:

**1. "Using Mock Responses" angezeigt:**
- ✅ Überprüfen Sie Ihren API-Key in der `.env` Datei
- ✅ Stellen Sie sicher, dass Ihr Z.AI Konto Guthaben hat
- ✅ Überprüfen Sie die API-Endpoint-URL
- ✅ Prüfen Sie Ihre Internetverbindung

**2. Fehler 429 (Rate Limit/Insufficient Credits):**
```json
{"error":{"code":"1113","message":"您的账户已欠费，请充值后重试。"}}
```
**Lösung**: Z.AI Konto aufladen - das Konto hat kein Guthaben mehr

**3. Enhanced Features nicht verfügbar:**
- ✅ API-Key-Berechtigungen überprüfen
- ✅ Feature-spezifische API-Limits prüfen
- ✅ Konto-Status bei Z.AI überprüfen

**4. Web Search Fehler:**
```json
{"error":{"message":"JSON parse error: Cannot deserialize..."}}
```
**Lösung**: API-Request-Format überprüfen - wird automatisch von der App behandelt

## 🔧 Enhanced Entwicklung

### Erweiterte Projektstruktur:
```
src/
├── components/
│   └── spark/
│       ├── EnhancedSparkChatInterface.tsx  # Haupt-Chat mit Z.AI Features
│       ├── QuickAccessDropdown.tsx         # Enhanced Template System
│       ├── SparkButton.tsx                 # UI-Komponenten
│       └── ...
├── config/
│   └── api.ts                              # Z.AI API-Konfiguration
├── services/
│   └── apiService.ts                       # Full Z.AI API Integration
└── ...
```

### Wichtige Enhanced Dateien:

- **[`src/services/apiService.ts`](src/services/apiService.ts)**: Vollständige Z.AI API-Integration mit erweiterten Features
- **[`src/config/api.ts`](src/config/api.ts)**: Enhanced Konfigurationsmanagement
- **[`src/components/spark/EnhancedSparkChatInterface.tsx`](src/components/spark/EnhancedSparkChatInterface.tsx)**: Chat-Interface mit Z.AI Features Panel

### Enhanced Debug-Modus aktivieren:
```env
VITE_DEBUG_MODE=true
```

Im Debug-Modus werden detaillierte Logs für alle erweiterten Features in der Browser-Konsole angezeigt:
- 🚀 Enhanced Z.AI API Requests
- 🎭 Fallback System Aktivierung
- ✨ Feature Detection Logic
- 🌐 Web Search Aktivierung
- 🧠 Deep Thinking Prozesse

## 📊 Enhanced API Response Handling

### Erfolgreiche Enhanced Response:
```json
{
  "success": true,
  "data": {
    "message": "Enhanced KI-generierte Antwort mit Web-Suche...",
    "id": "unique-message-id",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "featuresUsed": ["webSearch", "deepThinking"]
  }
}
```

### Enhanced Feature Detection Response:
```json
{
  "featureDetection": {
    "webSearchNeeded": true,
    "deepThinkingNeeded": false,
    "toolsNeeded": false,
    "confidence": 0.95
  }
}
```

## 🏗️ Enhanced Build für Produktion

```bash
# Build mit allen erweiterten Features
npm run build

# Environment-spezifische Builds
npm run build:production
npm run build:staging
```

Die produktionsbereiten Dateien mit allen Enhanced Features befinden sich im `dist/` Ordner.

## 🎨 Enhanced Styling System

Das erweiterte Design-System umfasst:
- **Enhanced Tailwind CSS**: Benutzerdefinierte Z.AI Farbpalette
- **Electric Blue Theme**: Modernes Gradient-System
- **Advanced Animations**: Framer Motion Erweiterungen
- **Responsive Design**: Optimiert für alle Geräte
- **Accessibility**: Vollständige ARIA-Unterstützung

### Custom CSS Variables:
```css
:root {
  --electric-blue: 59 130 246;
  --electric-purple: 139 92 246;
  --gradient-primary: linear-gradient(135deg, #3b82f6, #8b5cf6);
}
```

## 🧪 Enhanced Testing

### Feature Testing Commands:
```bash
# Vollständige Test-Suite ausführen
npm test

# API-Integration testen
npm run test:api

# Enhanced Features testen
npm run test:features
```

### Manuelle Test-Szenarien:
1. **Web Search**: Mit aktuellen Event-Anfragen testen
2. **Deep Thinking**: Mit komplexen Analyse-Anfragen testen  
3. **AI Tools**: Mit mathematischen Berechnungen testen
4. **Fallback System**: Mit ungültigen API-Keys testen
5. **Feature Detection**: Verschiedene Anfrage-Typen testen

## 🔒 Enhanced Security & Error Handling

### Umfassende Fehlerbehandlung:
- **API-Verbindungsfehler**: Intelligente Retry-Logik
- **Ungültige API-Antworten**: Graceful Error Handling
- **Feature-Nicht-Verfügbarkeit**: Intelligente Feature-Deaktivierung
- **Netzwerk-Probleme**: Offline-Modus-Unterstützung
- **Rate Limiting**: Automatische Backoff-Strategien

### Sicherheits-Features:
- **API-Key-Schutz**: Sichere Umgebungsvariablen-Behandlung
- **Request-Validierung**: Input-Sanitization
- **Response-Filterung**: Content-Security-Maßnahmen

## 📈 Performance Optimization

### Erweiterte Features:
- **Context Caching**: Intelligente Konversationshistorie-Verwaltung
- **Request Optimization**: Effiziente API-Aufrufe
- **Feature Detection**: Intelligente Ressourcen-Zuteilung
- **Lazy Loading**: Komponenten-basiertes Laden
- **Memory Management**: Optimierte State-Behandlung

## 🆕 Was ist neu in der Enhanced Version

### ✨ Neueste Erweiterungen:
- **Vollständige Z.AI GLM-4.5 Integration**: Komplette API-Feature-Unterstützung
- **Echtzeit Web-Suche**: Live Internet-Informationszugang
- **Deep Thinking Modus**: Erweiterte KI-Denkfähigkeiten
- **Intelligente Feature-Erkennung**: Automatische Feature-Aktivierung
- **Enhanced UI Controls**: Live Feature-Management-Panel
- **Verbesserte Templates**: Z.AI-spezifische Schnellzugriffe
- **Erweiterte Fehlerbehandlung**: Intelligentes Fallback-System
- **Performance-Optimierung**: Enhanced API-Effizienz

## 🤝 Contributing to Enhanced Features

1. Repository forken
2. Feature-Branch erstellen: `git checkout -b feature/enhanced-zai`
3. Enhanced Features mit Tests implementieren
4. Dokumentation aktualisieren
5. Pull Request mit detaillierter Feature-Beschreibung erstellen

### Entwicklungs-Richtlinien:
- TypeScript Best Practices befolgen
- Umfassende Fehlerbehandlung einschließen
- Feature-Detection-Logik hinzufügen
- API Service Integration aktualisieren
- Mit echter Z.AI API testen

## 📞 Enhanced Support

### Z.AI API Support:
- **API Dokumentation**: [Z.AI API Docs](https://open.bigmodel.cn/dev/api)
- **Feature Requests**: Detaillierte GitHub Issues erstellen
- **Bug Reports**: API Response Logs einschließen
- **Integration Help**: Community Support verfügbar

### Kontakt & Ressourcen:
- **GitHub Issues**: [Issue erstellen](https://github.com/MimiTechAi/spark-ai-experience/issues)
- **Email Support**: <EMAIL>
- **Dokumentation**: Vollständige Feature-Dokumentation enthalten
- **Community**: Unserer Entwickler-Community beitreten

## 📚 Zusätzliche Enhanced Ressourcen

- [Z.AI Enhanced API Dokumentation](https://open.bigmodel.cn/dev/api)
- [GLM-4.5 Model Documentation](https://open.bigmodel.cn/dev/howuse/model)
- [React + Vite Best Practices](https://vitejs.dev/guide/)
- [TypeScript Advanced Patterns](https://www.typescriptlang.org/docs/)

---

**🚀 Enhanced by MiMi Tech AI - Powered by Z.AI GLM-4.5**

*Erleben Sie die Zukunft der KI-Konversationen mit Echtzeit-Web-Suche, Deep Thinking und erweiterten AI-Tools-Integration.*

**Entwickelt mit ❤️ von MimiTech AI**

## 💳 Subscription & Usage Management (Z.AI-basiert)

Um eine nachhaltige und kostendeckende Nutzung der Z.AI API zu gewährleisten, nutzt Spark AI ein abonnementbasiertes Modell mit integriertem Usage- und Kostenmonitoring.

### ✅ Tarifstruktur & Marge

| Plan           | Preis/Monat | Tokenlimit       | Modelle (Z.AI API)         | API-Kosten (geschätzt) | Marge     |
|----------------|-------------|------------------|-----------------------------|-------------------------|-----------|
| Explorer       | 0 €         | 100.000 Token     | GLM-4.5-Flash (kostenfrei)  | 0 €                     | —         |
| Creator        | 19 €        | 750.000 Token     | GLM-4.5, GLM-4.5-Air        | ca. 0,90 €              | ×20       |
| Architect      | 49 €        | 3.000.000 Token   | GLM-4.5, GLM-4.5-AirX       | ca. 3,60 €              | ×13       |
| Omnia          | 299 €       | 25.000.000 Token  | GLM-4.5, 4.5-X, CogVideoX-2 | ca. 30 €                | ×10       |

**Overusage (Add-on):**
- +250k Token → 1 €
- +1 Mio Token → 3 €
- +Video (Vidu/CogVideoX) → ab 0,50 €/Stück

### 🚦 Rate Limiting & Monitoring

Um API-Kostenrisiken zu vermeiden, implementiert Spark AI folgende Kontrollmechanismen:

- **Pro-User-Rate-Limiting** je nach Tarif (z. B. 1–5 Requests/Sekunde)
- **Tokenzählung pro User** (Input + Output) über alle Modelle hinweg
- **Softlimit bei 80 %**, automatischer Hinweis im Frontend
- **Hardlimit bei 100 %** mit Upgrade-/Add-on-Option

### 🧾 Abo-Verwaltung (z. B. via Stripe)

- Jede Nutzer:in ist einem Abo-Tier zugeordnet (via Stripe Subscription ID)
- In der Datenbank hinterlegt:
  - `tier`
  - `monthly_token_limit`
  - `used_tokens`
  - `billing_cycle_end`
- Automatische Synchronisierung mit Stripe-Webhooks:
  - `invoice.paid`, `subscription.updated`, `checkout.session.completed`

### 🔐 Zugriffskontrolle nach Plan

- **GLM-4.5-X, CogVideoX, Whisper, Vidu** nur für zahlende Nutzer mit „Architect“ oder „Omnia“
- **Free-Nutzer** haben Zugriff ausschließlich auf `glm-4.5-flash`
- **Feature-basierte Zugriffskontrolle** im API-Service implementiert

Diese Maßnahmen sichern Skalierbarkeit und einen nachhaltigen Cashflow bei Nutzung kostenpflichtiger Z.AI Modelle.
