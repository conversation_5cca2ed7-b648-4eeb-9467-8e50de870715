# Environment Variables Setup Guide

This guide provides step-by-step instructions for setting up environment variables for the Spark AI Experience application across different deployment environments.

## Quick Start

1. **Copy the example file**:
   ```bash
   cp .env.example .env
   ```

2. **Configure your API key**:
   ```bash
   # Edit .env and add your Z.AI API key
   VITE_API_KEY=your_actual_api_key_here
   ```

3. **Start development**:
   ```bash
   npm run dev
   ```

## Environment Files Structure

```
├── .env.example          # Template with all variables
├── .env.development      # Development-specific config
├── .env.staging          # Staging-specific config  
├── .env.production       # Production-specific config
└── .env                  # Local overrides (not committed)
```

## Required Variables for Each Environment

### Development
```bash
# Minimal setup for development
VITE_API_KEY=your_zai_api_key
VITE_DEBUG_MODE=true
```

### Staging
```bash
# Required for staging deployment
VITE_API_KEY=your_staging_api_key
POSTGRES_URL=your_staging_database_url
POSTGRES_PRISMA_URL=your_staging_prisma_url
POSTGRES_URL_NON_POOLING=your_staging_non_pooling_url
```

### Production
```bash
# Required for production deployment
VITE_API_KEY=your_production_api_key
POSTGRES_URL=your_production_database_url
POSTGRES_PRISMA_URL=your_production_prisma_url
POSTGRES_URL_NON_POOLING=your_production_non_pooling_url
VITE_DEBUG_MODE=false
VITE_ANALYTICS_ENABLED=true
```

## Vercel Deployment Setup

### 1. Set Environment Variables in Vercel Dashboard

1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add the following variables for each environment:

**Production Environment:**
```
VITE_API_KEY = your_production_api_key
POSTGRES_URL = your_vercel_postgres_url
POSTGRES_PRISMA_URL = your_vercel_postgres_prisma_url
POSTGRES_URL_NON_POOLING = your_vercel_postgres_non_pooling_url
VITE_CORS_ORIGINS = your-domain.vercel.app
```

**Preview Environment:**
```
VITE_API_KEY = your_staging_api_key
POSTGRES_URL = your_staging_postgres_url
POSTGRES_PRISMA_URL = your_staging_postgres_prisma_url
POSTGRES_URL_NON_POOLING = your_staging_postgres_non_pooling_url
VITE_CORS_ORIGINS = your-app-*.vercel.app
```

### 2. Using Vercel CLI

```bash
# Add production variables
vercel env add VITE_API_KEY production
vercel env add POSTGRES_URL production

# Add preview variables  
vercel env add VITE_API_KEY preview
vercel env add POSTGRES_URL preview

# List all variables
vercel env ls
```

## Database Setup (Vercel Postgres)

### 1. Create Vercel Postgres Database

```bash
# Using Vercel CLI
vercel postgres create spark-ai-db

# Or through Vercel Dashboard:
# Storage → Create Database → Postgres
```

### 2. Get Connection Strings

After creating the database, Vercel will provide three connection strings:

- `POSTGRES_URL` - Standard connection with pooling
- `POSTGRES_PRISMA_URL` - Optimized for Prisma ORM
- `POSTGRES_URL_NON_POOLING` - Direct connection without pooling

Add these to your environment variables.

## Security Best Practices

### 1. API Key Management
- Use different API keys for different environments
- Never commit API keys to version control
- Rotate API keys regularly
- Monitor API key usage

### 2. Database Security
- Use connection pooling in production
- Enable SSL for database connections
- Regular database backups
- Monitor database performance

### 3. CORS Configuration
```bash
# Development
VITE_CORS_ORIGINS=localhost,127.0.0.1

# Staging  
VITE_CORS_ORIGINS=localhost,127.0.0.1,your-app-*.vercel.app

# Production
VITE_CORS_ORIGINS=your-domain.vercel.app
```

## Validation and Testing

### 1. Check Configuration Status
The application automatically validates configuration on startup. Check the browser console for:

```
✅ Application initialized successfully
🔧 Configuration Status: { status: 'healthy', environment: 'development' }
```

### 2. Manual Validation
```javascript
// In browser console
import { configValidator } from './src/config/validation';
console.log(configValidator.generateConfigurationReport());
```

### 3. Health Check
```javascript
// Check application readiness
import { getApplicationReadiness } from './src/config/startup';
console.log(getApplicationReadiness());
```

## Troubleshooting

### Common Issues

#### 1. API Key Format Error
```
Error: VITE_API_KEY must follow Z.AI format: 32chars.16chars
```
**Solution**: Ensure your API key follows the format: `abcd1234...32chars.efgh5678...16chars`

#### 2. Database Connection Error
```
Error: Database connection string appears invalid
```
**Solution**: Verify your PostgreSQL connection string format:
```
postgresql://username:password@host:port/database
```

#### 3. CORS Error
```
Error: CORS policy blocked the request
```
**Solution**: Add your domain to `VITE_CORS_ORIGINS`:
```bash
VITE_CORS_ORIGINS=localhost,127.0.0.1,yourdomain.com
```

#### 4. Environment Detection Issues
```
Warning: Environment detected as 'staging' but expected 'production'
```
**Solution**: Check your `NODE_ENV` and `VERCEL_ENV` variables.

### Debug Mode

Enable debug mode for detailed logging:
```bash
VITE_DEBUG_MODE=true
```

This will show:
- Configuration loading details
- Validation results
- API request/response information
- Security events

## Migration from Previous Setup

If you're migrating from a previous configuration:

1. **Backup existing `.env` file**:
   ```bash
   cp .env .env.backup
   ```

2. **Update to new structure**:
   ```bash
   cp .env.example .env
   # Copy your values from .env.backup
   ```

3. **Test configuration**:
   ```bash
   npm run dev
   # Check console for validation results
   ```

4. **Update Vercel variables**:
   - Remove old variables
   - Add new variables following this guide

## Support

For configuration issues:

1. Check the browser console for validation errors
2. Review the `ENVIRONMENT.md` documentation
3. Use the built-in validation tools
4. Check Vercel deployment logs

## Environment Variables Reference

See `ENVIRONMENT.md` for complete documentation of all available environment variables, their formats, and validation rules.